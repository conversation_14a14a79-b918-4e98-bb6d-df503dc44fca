.reglementation {
    width: 100%;
    form#views-exposed-form-reglementation-page-1 {
        margin-bottom: 20px;
    }
    .card {
        padding: 30px 45px;
        height: 100%;
        img {
            width: 46px;
        }
        .card-body {
            padding: 0;
            p {
                font-family: $rubik-bold;
                font-size: 20px;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 700;
                }
            }
            & > div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: rem(15) 0;
                flex-wrap: wrap;
                gap: 10px;
                a {
                    font-family: $quicksand-regular;
                    background: #ebf1fa;
                    color: $primary;
                    text-transform: none;
                    font-size: 16px;
                    padding: 14px 18px;
                    border-width: 0;
                    box-shadow: none;
                    line-height: 1.2;
                    zoom: .85;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                        font-weight: 400;
                    }
                    i {
                        animation: none;
                    }
                    &:hover {
                        background: $primary;
                        color: $white;
                    }
                }
            }
            & > a {
                text-transform: none;
                // height: 50px;
                min-width: 226px;
                .fa-download {
                    padding-right: 4px;
                }
            }
        }
        @media(max-width:600px) {
            padding: 30px;
        }
    }
    &.secteur {
        .card {
            cursor: pointer;
            .card-body {
                padding: 20px 0;
                p {
                    transition: all 450ms ease;
                }
            }
            &:hover {
                .card-body {
                    p {
                        color: $primary;
                    }
                }
            }
        }
    }
    &.presse {
        .card {
            padding: 30px 45px;
            height: 100%;
            .card-body {
                & > div {
                    align-items: flex-start;
                    gap: 50px;
                    flex-wrap: nowrap;
                    a {
                        font-family: $rubik-bold;
                        font-weight: bold;
                        background: $secondary;
                        color: $white;
                        border-width: 1px;
                        border-color: $secondary;
                        zoom: 1;
                        min-width: rem(226);
                        html[dir="rtl"] & {
                            font-family: "Cairo", sans-serif;
                        }
                        &:hover {
                            background: transparent;
                            color: $secondary;
                        }
                    }
                    @media(max-width:768px) {
                        flex-wrap: wrap;
                        gap: 20px;
                    }
                }
            }
            
        }
    }
    //sup Secteur
    .suppSecteur & {
        form {
            fieldset[id^="edit-field-secteur-target-id--"]  {
               display: none !important; 
            }
             
        }
    }
}