.liens-utiles {
    width: 100%;
    @media(max-width:767px) {
        .container{
          max-width: 100%;
        }
    }
    .swiper.liens-utile{
        padding-bottom: 80px;
        .picture {
            background: $white;
            border-radius: 10px;
            padding: 20px 0;
            box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
            justify-content: center;
            align-items: center;
            display: flex;
            p, span {
                color: white;
            }
            span {
                display: block;
                font-family: $rubik-bold;
                font-weight: bold;
                font-size: rem(30);
                padding: 20px 0 10px;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                }
            }
            img {
                transform: scale(1) !important;
            }
        }
       
        @media(max-width: 600px) {
            padding-bottom: 0;
        }
        @include swiper-dots;
    }
    @include container-slide;
}