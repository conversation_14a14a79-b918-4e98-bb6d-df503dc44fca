.filter-e-service {
    width: 100%;
    form {
        // display: grid;
        // grid-template-columns: 1fr 1fr 260px;
        background: $white;
        box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
        border-radius: 14px;
        gap: 20px;
        padding: 40px 150px 40px 40px;
        margin-bottom: 40px;
        input[type="submit"] {
            font-family: $rubik-bold;
            font-weight: bold;
            margin: initial;
            width: 100%;
            height: 100%;
            border-radius: 30px;
            background: $secondary;
            color: $white;
            font-size: rem(20);
            padding-left: 40px;
            text-align: left;
            text-transform: uppercase;
            transition: background 450ms ease;
            html[dir="rtl"] & {
                font-family: "Cairo", sans-serif;
            }
            &:hover {
                background: $primary;
            }
        }
        .wrap-link {
            position: relative;
            &:after {
                content: "";
                position: absolute;
                mask: url($images-path + 'icons/icon-search.svg') no-repeat 0 0;
                width: 16px;
                height: 16px;
                top: 50%;
                right: 50px;
                transform: translateY(-50%);
                background: $white;
            }
        }
       
    }
}