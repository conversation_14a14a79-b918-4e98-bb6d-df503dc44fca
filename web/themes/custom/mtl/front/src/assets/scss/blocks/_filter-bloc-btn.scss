.filter-buttons {
    width: 100%;
    padding-bottom: 0;
    form#views-exposed-form-e-services-page-1 {
        margin-bottom: 0;
    }
    .bloc-btn {
        width: 100%;
        background-color: $white;
        box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
        border-radius: 14px;
        padding: 30px;
        .h4-title {
            font-size: rem(15);
            color: $black;
        }
        &__secteur {
            width: 100%;
            padding-right: 80px;
            margin-bottom: 30px;
            &--btns {
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 10px;
                a {
                    font-size: 15px;
                    padding: 10px 0;
                    &:nth-child(2) {
                        grid-column: 2 / 4;
                    }
                }
            }
        }
        &__profil {
            width: 100%;
            &--btns {
                a {
                    &:nth-child(2) {
                        grid-column: 2 / 3;
                    }
                }
            }
        }
    }
    @media(max-width: 640px) {
        .container {
            padding: 0;
        }
    }
}