// -----------------------------------------------------------------------------
// This file contains all styles related to the footer of the site/application.
// -----------------------------------------------------------------------------
.footer {
    position: relative;
    width: 100%;
    background-color: $primary;
    padding: 0 30px 50px 30px;
    z-index: 0;
    &:before {
        content: "";
        background: $colorBody;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 80px;
        z-index: -1;
    }
    @media(max-width:768px) {
        padding: 0 0 50px 0;
        &:before {
        height: 120px;
    } 
    }
    .newsletter {
        width: 100%;
        display: flex;
        align-items: center;
        // flex-wrap: wrap;
        background: $white;
        border-radius: 14px;
        margin-bottom: 50px;
        padding: 40px 70px;
        gap: 10px;
        h3 {
            font-family: $rubik-bold;
            margin-bottom: 0;
            color: $primary;
            font-size: 28px;
            html[dir="rtl"] & {
                font-family: "Cairo", sans-serif;
                font-weight: 700;
            }
        }
        form {
            background: transparent;
            display: flex;
            padding: 0;
            gap: 15px;
             box-shadow: none;
            input[type="submit"] {
                width: 100%;
                flex: 1;
                background: $secondary;
                color: $white;
                text-transform: uppercase;
                transition: all 450ms ease;
                &:hover {
                    background: $primary;
                }
            }
            div:first-child {
                flex: 3;
            }
            div:first-child ~ div {
                flex: 1;
                width: 260px;
            }
        }
        @media(max-width:768px) {
            flex-direction: column;
            padding: 30px;
            h3 {
                font-size: 22px;
                margin-bottom: 10px;
            }
            form {
                flex-direction: column;
                div:first-child {
                    flex: 1;
                }
                div:first-child ~ div {
                    width: 100%;
                }
            }
        }
    }
    &__top, &__bottom {
        display: flex ;
        align-items: center;
        justify-content: space-between;
        gap: rem(40);
        @media only screen and (max-width: 600px) {
            flex-direction: column;
            align-items: flex-start;
        }
    }
    &__top {
        margin-bottom: rem(50);
        &--rsociaux {
            a {
                &:hover {
                    &:before {
                        color: rgba($white, .7) !important;
                    } 
                }
            }
        }
    }
    &__middle {
        & > ul {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: rem(20);
            a {
                position: relative;
                transition: all 450ms ease;
                color: rgba($white, .7);
                font-size: rem(14);
                display: inline-block;
                &:after {
                    content: "";
                    position: absolute;
                    bottom: -2px;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                    width: 0;
                    height: .8px;
                    background-color: rgba($white, .7);
                    transition: all 450ms ease-in;
                }
                &:hover{
                    color: $white;
                    &:after {
                        width: 100%;
                        background-color: $white;
                    }
                }
            }
            & > li {
                & > a {
                    font-family: $rubik-bold;
                    font-weight: bold;
                    font-size: rem(14);
                    margin-bottom: rem(20);
                    pointer-events: none !important;
                    display: block;
                    color: $white;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                    }
                }
            }
            li {
                line-height: 1.3;
                &:not(:last-child) {
                    margin-bottom: rem(8);
                }
            }
            @media only screen and (max-width: 1024px) {
                grid-template-columns: repeat(4, 1fr);
                & > li {
                    & > a {
                        margin-bottom: 5px;
                    }
                }
            }
            @media only screen and (max-width: 768px) {
                grid-template-columns: repeat(3, 1fr);
            }
            @media only screen and (max-width: 600px) {
                // grid-template-columns: repeat(1, 1fr);
                // gap: 0;

                // a {
                //     &:after {
                //         display: none;
                //     }
                // }
                // & > li {
                //     margin-bottom: 0;
                //     background-color: rgba($white, .1);
                //     border-radius: rem(10);
                //     & > a {
                //         position: relative;
                //         display: block;
                //         height: auto !important;
                //         font-size: rem(16);
                //         pointer-events:all !important;
                //         padding: rem(20) rem(15);
                //         color: $white;
                //         &:before {
                //             content: "\e903";
                //             font-family: 'icomoon';
                //             position: absolute;
                //             right: rem(15);
                //             top: rem(10);
                //             font-size: rem(8);
                //             color: $white;
                //             top: 50%;
                //             transform: translateY(-50%);
                //             transition: transform 350ms ease;
                //         }
                //         &.active {
                //             color: $white;
                //             &:before {
                //                transform: translateY(-50%) rotateZ(180deg);
                //             }
                //         }
                //         &:hover {
                //             color: $white;
                //         }
                //     }
                //     & > ul {
                //         display: none;
                //         padding: 0 rem(30) rem(30);
                //     }
                // }
                @include header-footer-mobile;
            }
        }
    }

    &__bottom {
        p {
            color: rgba($white, .5);
            font-size: rem(14);
            &:last-child {
                font-size: 13px;
            }
        }
        a {
            color: rgba($white, .5);
            transition: color 450ms ease;
            &:hover {
                color: $white;
            }
        }
        @media only screen and (max-width: 600px) {
            gap: rem(10);
        }
    }
}