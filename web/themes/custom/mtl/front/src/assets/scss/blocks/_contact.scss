.contact {
    width: 100%;
    padding-bottom: 60px;
    .row {
        &.card {
            flex-direction: row;
            iframe {
                width: 100%;
                @media(max-width: 768px) {
                   padding-left: 0;
                }
            }
        }
    }
    .contact-infos {
        padding: 30px;
        h2 {
            color: $primary;
            margin-bottom: 20px;
        }
        p {
            color: $gray;
            font-size: 18px;
            &:not(:last-child) {
                margin-bottom: 10px;
            }
        }
    }
}
#webform-submission-contact-add-form {
    width: 100%;
    display: grid;
    grid-template-columns: (repeat(2, 1fr));
    gap: 20px;
    padding: 60px;
    input,textarea {
        font-family: $quicksand-regular;
        font-size: 16px;
        color: $black;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
        }
    }
    label {
        font-size: 16px;
        color: #717272;
        &.form-required {
            position: relative;
            &::after {
                content: "*";
                color: red;
                font-size: 18px;
            }
        }
    }
    // input[required] + label {
    //     position: relative;
    //     &::before {
    //         content: "*";
    //         color: red;
    //         font-size: 18px;
    //     }
    // }
    legend span {
        font-size: 16px;
        color: #717272;
    }
    .form-contact {
        display: flex;
        flex-direction: row;
        & > div {
            flex: 1;
        }
        iframe {
            width: 100%;
        }
    }
    .js-form-item-sujet {
        grid-row: 3;
        grid-column: 1 / -1;
    }
    .fieldgroup,
    .js-form-item-message, 
    .js-form-item-cgu,
    #edit-required-message-notice,
    #edit-loi {
        grid-column: 1 / -1;
        color: #717272;
        font-size: 14px;
        label[for^="edit-type-du-message"]
        {
            // color: red;
            position: relative;
            padding-left: 20px !important;
            &:after {
                content: "";
                position: absolute;
                width: 15px;
                height: 15px;
                left: 0;
                top: 4px;
                border-radius: 100%;
                border: 1px solid #717272;
                transition: all 450ms ease;
            }
            &:before {
                content: "";
                position: absolute;
                background: $primary;
                width: 10px;
                height: 10px;
                border-radius: 100%;
                top: 6px;
                left: 2.5px;
                z-index: 1;
                transition: all 450ms ease;
                display: none;
            }
        }
        input[type="radio"] {
            opacity: 0;
        }
        input[type="radio"]:checked + label{
            &:before {
                display: block;
            }
        }
    }
    .form-item-cgu {
        display: flex;
        label[for="edit-cgu"] {
            position: relative;
            font-size: 0;
            text-indent: -9999px;
            &:after {
                content: "";
                position: absolute;
                width: 21px;
                height: 20px;
                left: 0;
                top: 3px;
                border-radius: 4px;
                border: 1px solid #717272;
                transition: all 450ms ease;
            }
            &:before {
                content: "";
                position: absolute;
                mask: url($images-path + 'icons/check.svg') no-repeat 0 0;
                background: $white;
                width: 14px;
                height: 13px;
                top: 4px;
                left: 3px;
                z-index: 1;
                transition: all 450ms ease;
                display: none;
            }
        }
        input[type="checkbox"] {
            position: absolute;
            width: 23px;
            height: 23px;
            opacity: 0;
        }
        input[type="checkbox"]:checked + label {
            &:after {
                background: $primary;
                border-color: $primary;
            }
            &:before {
                display: block;
            }
        }
        .description {
            margin-left: 30px;
        }
    }
    .js-form-item-cgu .description {
        color: #717272;
        font-size: 14px;
        a {
            position: relative;
            color: #717272;
            font-size: 14px;
            &:after {
                content: "";
                position: absolute;
                width: 100%;
                height: 1px;
                left: 0;
                bottom: -2px;
                background-color: $primary;
                transform-origin: left;
                transform: scale(1);
                transition: transform 600ms ease;
            }
            &:hover {
                color: $primary;
                &:after {
                    transform: scale(0);
                    transform-origin: right;
                }
            }
        }
    }
    input[type="submit"] {
        font-family: $quicksand-bold;
        font-size: 18px;
        width: 220px;
        grid-column: 1 / -1;
        background-color: $primary;
        color: $white;
        border-color: $primary;
        border-color: transparent;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 700;
        }
        &:hover {
            background-color: $secondary;
        }
    }
    input[type="radio"] {
        display: block;
    }
    #edit-type-du-message--wrapper {
        .fieldset-wrapper {
            div {
                display: flex;
            }
            .webform-options-display-one-column {
                gap: 150px;
                label {
                    padding-left: 5px;
                }
                @media(max-width: 768px) {
                   gap: 20px;
                   label {
                    font-size: 14px;
                   }
                }
            }
        }
    }
    .required-alert sup {
        color: #CB0000;
        font-size: 16px;
        top: 0;
    }
    @media(max-width: 768px) {
        grid-template-columns: (repeat(1, 1fr));
        padding: 40px;
    }
}