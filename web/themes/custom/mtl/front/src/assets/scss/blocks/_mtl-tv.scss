.bloc-video {
  width: 100%;

  @media(max-width:767px) {
    .container, .container-sm {
      max-width: 100%;
    }
  }
  .mtl-tv {
    width: 100%;
    .swiper-video {
      padding-bottom: rem(30);
      @include swiper-dots;
    }
    &__items {
      &--item {
      .content {
          position: relative;
          overflow: hidden;
          background: $white;
          border-radius: 15px;
          box-shadow: rgba(50, 50, 93, 0.16) 0px 6px 4px -2px;
          .picture {
            position: relative;
          }
          
          &:after {
              content: '';
              // background: linear-gradient(to top, rgba($primary,.8), transparent);
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              transition: .5s background-color ease-in-out;
              opacity: 0;
              visibility: hidden;
          }
          
          img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 14px 14px 0 0;
              transform: scale(1) translateX(0);
              transition: transform 1.5s ease;
          }
          .icon-play {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            width: 60px;
            height: 60px;
            mask:url(../../assets/img/icons/icon-material-play-video.svg) no-repeat center / 100%;
            background-color: $white;
            transition: background 450ms ease;
          }
          .sub-content {
            padding: 35px 20px;
            // h3 {
            //   font-size: 22px;
            // }
            .time {
              font-family: $quicksand-regular;
              font-size: rem(18);
              color: #656565;
              html[dir="rtl"] & {
                  font-family: "Cairo", sans-serif;
                  font-weight: 400;
              }
            }
          }
          @media(max-width: 600px) {
            display: flex;
            .icon-play {
              width: 50px;
              height: 50px;
            }
            .picture {
              img {
                min-height: 169px;
              }
            }
            .sub-content {
              padding: 20px 14px;
              h3 {
                font-size: 16px;
                margin-bottom: 0;
              }
            }
          }
        }
        &:hover {
            .picture {
                &:after {
                    opacity: 1;
                    visibility: visible;
                }
                img {
                transform: scale(1.02) translateX(2%);
                }
                .icon-play {
                  background: $primary;
                }
            }
        }
      }
      // @media only screen and (max-width: 768px) {
      //   flex-direction: column;
      //   &--item {
      //     width: 100%;
      //   }
      // }
      // @media only screen and (max-width: 480px) {
      //   &--item {
      //     .content {
      //       display: flex;
      //     }
      //   }
      // }
    }
    
  }
}

@keyframes pulsate-btn {
  0% {
      transform: scale(0.6, 0.6);
      opacity: 1;
  }
  100% {
      transform: scale(1, 1);
      opacity: 0;
  }
}


/* Video Modal
-----------------------------------------*/
.video-modal,
.video-modal .overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 3000;
}

.video-modal {
  overflow: hidden;
  position: fixed;
  opacity: 0.0;
  transform: translate(500%, 0%);
  transition: transform 0s linear 0s;
  display: flex;
  align-items: center;
  transform-style: preserve-3d;
}
.video-modal .overlay {
  z-index: 0;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0.0;
  transition: opacity 0.2s ease-out 0.05s;
}
.video-modal-content {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  margin: 0 auto;
  overflow-y: visible;
  background: $black;
  width: 100%;
  max-width: rem(960);
  aspect-ratio: 16 / 9;
}

@media (max-width: 640px) {
  .video-modal-content {
    width: calc(100% - 1em);
    padding-top: calc((100% - 1em) * 0.5625);
  }
}

iframe#youtube {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1; 
  background: $black;
  box-shadow: 0px 2px 16px rgba(0, 0, 0, 0.5);
}

.show-video-modal .video-modal {
  opacity: 1.0;
  transform: translate(0%, 0%);
}

.show-video-modal .video-modal .overlay {
  opacity: 1.0;
}

.show-video-modal .video-modal-content {
  transform: translate(0%, 0%);
}

.close-video-modal {
  display: block;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 100%;
  top: 0;
  cursor: pointer;
  z-index: 9999;
  color: $white;
  width: 20px;
  height: 20px;
  border-radius: 100%;
  background: $primary;
  zoom: 1.4;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}