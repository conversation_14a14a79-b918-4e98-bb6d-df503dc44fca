.infos-region {
    width: 100%;
    .card {
        padding: 38px;
    }
    h3, h4 {
        font-family: $rubik-bold;
        font-size: 18px;
        color: $black;
        margin-bottom: 10px;
        line-height: 1;
        text-transform: none;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 700;
        }
    }
    h3{
        font-family: $rubik-bold;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 700;
        }
    }
    h4 {
        font-family: $rubik-regular;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
        }
    }
    ul {
        margin: 0;
        li {
            i {
                color: $primary;
                margin-right: 5px;
            }
            &:not(:last-child) {
                margin-bottom: 8px;
            }
            a {
                font-family: $quicksand-regular;
                font-size: 16px;
                transition: all 450ms ease;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 400;
                }
                &:hover {
                    color: $secondary;
                }
            }
        }
    }
}
.bilan {
    .card {
        flex-direction: row;
        justify-content: space-between;
        padding: 30px 20px;
        gap: 20px;
        .picture {
            display: flex;
            flex: 1;
            align-items: center;
        }
        img {
            border-radius: 0;
            margin-right: 15px;
        }
        a {
            background: transparent;
            color: $secondary;
            i {
                transform: none !important;
                color: $secondary;
            }
            &:hover {
                color: $white;
                i {
                    color: $white;
                }
            }
        }
        @media(max-width: 600px) {
            flex-direction: column;
        }
    }
}