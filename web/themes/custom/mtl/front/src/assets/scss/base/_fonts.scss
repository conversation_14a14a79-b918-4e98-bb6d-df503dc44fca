// -----------------------------------------------------------------------------
// This file contains all @font-face declarations, if any.
// -----------------------------------------------------------------------------

// Fonts Rubik
@font-face {
    font-family: 'rubikRegular';
    src: url($fonts-path + 'police/rubikRegular/Rubik-Regular.eot');
    src: url($fonts-path + 'police/rubikRegular/Rubik-Regular.eot?#iefix') format('embedded-opentype'),
        url($fonts-path + 'police/rubikRegular/Rubik-Regular.woff2') format('woff2'),
        url($fonts-path + 'police/rubikRegular/Rubik-Regular.woff') format('woff'),
        url($fonts-path + 'police/rubikRegular/Rubik-Regular.ttf') format('truetype'),
        url($fonts-path + 'police/rubikRegular/Rubik-Regular.svg#Rubik-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'rubikBold';
    src: url($fonts-path + 'police/rubikBold/Rubik-Bold.eot');
    src: url($fonts-path + 'police/rubikBold/Rubik-Bold.eot?#iefix') format('embedded-opentype'),
        url($fonts-path + 'police/rubikBold/Rubik-Bold.woff2') format('woff2'),
        url($fonts-path + 'police/rubikBold/Rubik-Bold.woff') format('woff'),
        url($fonts-path + 'police/rubikBold/Rubik-Bold.ttf') format('truetype'),
        url($fonts-path + 'police/rubikBold/Rubik-Bold.svg#Rubik-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}


// Fonts Quicksand
@font-face {
    font-family: 'quicksandRegular';
    src: url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.eot');
    src: url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.eot?#iefix') format('embedded-opentype'),
        url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.woff2') format('woff2'),
        url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.woff') format('woff'),
        url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.ttf') format('truetype'),
        url($fonts-path + 'police/quicksandRegular/Quicksand-Regular.svg#Quicksand-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
  font-family: 'quicksandBold';
  src: url($fonts-path + 'police/quicksandBold/Quicksand-Bold.eot');
  src: url($fonts-path + 'police/quicksandBold/Quicksand-Bold.eot?#iefix') format('embedded-opentype'),
      url($fonts-path + 'police/quicksandBold/Quicksand-Bold.woff2') format('woff2'),
      url($fonts-path + 'police/quicksandBold/Quicksand-Bold.woff') format('woff'),
      url($fonts-path + 'police/quicksandBold/Quicksand-Bold.ttf') format('truetype'),
      url($fonts-path + 'police/quicksandBold/Quicksand-Bold.svg#Quicksand-Bold') format('svg');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

//Fonts icommon
@font-face {
    font-family: 'icomoon';
    src:  url($fonts-path + 'icomoon/icomoon.eot?gizk7e');
    src:  url($fonts-path + 'icomoon/icomoon.eot?gizk7e#iefix') format('embedded-opentype'),
      url($fonts-path + 'icomoon/icomoon.ttf?gizk7e') format('truetype'),
      url($fonts-path + 'icomoon/icomoon.woff?gizk7e') format('woff'),
      url($fonts-path + 'icomoon/icomoon.svg?gizk7e#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

//Font Arabe Regular
@font-face {
    font-family: 'Cairo';
    src: url($fonts-path + 'police/cairoRegular/Cairo-Regular.eot');
    src: url($fonts-path + 'police/cairoRegular/Cairo-Regular.eot?#iefix') format('embedded-opentype'),
        url($fonts-path + 'police/cairoRegular/Cairo-Regular.woff2') format('woff2'),
        url($fonts-path + 'police/cairoRegular/Cairo-Regular.woff') format('woff'),
        url($fonts-path + 'police/cairoRegular/Cairo-Regular.ttf') format('truetype'),
        url($fonts-path + 'police/cairoRegular/Cairo-Regular.svg#Cairo-Regular') format('svg');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

//Cairo Bold Regular
@font-face {
    font-family: 'Cairo';
    src: url($fonts-path + 'police/cairoBold/Cairo-Bold.eot');
    src: url($fonts-path + 'police/cairoBold/Cairo-Bold.eot?#iefix') format('embedded-opentype'),
        url($fonts-path + 'police/cairoBold/Cairo-Bold.woff2') format('woff2'),
        url($fonts-path + 'police/cairoBold/Cairo-Bold.woff') format('woff'),
        url($fonts-path + 'police/cairoBold/Cairo-Bold.ttf') format('truetype'),
        url($fonts-path + 'police/cairoBold/Cairo-Bold.svg#Cairo-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-icon-location-on:before {
    content: "\e90a";
    color: #656565;
  }
  .icon-icon-download:before {
    content: "\e900";
    color: #fff;
  }
  .icon-Icon-open-calendar:before {
    content: "\e901";
    color: #6b6b6b;
  }
  .icon-icon-calendar:before {
    content: "\e902";
    color: #3c77ce;
  }
  .icon-icon-arrow:before {
    content: "\e903";
    color: #fff;
  }
  .icon-icon-fleche:before {
    content: "\e904";
    color: #fff;
  }
  .icon-icon-facebook:before {
    content: "\e905";
    color: #fff;
  }
  .icon-icon-instagram:before {
    content: "\e906";
    color: #fff;
  }
  .icon-icon-linkedin:before {
    content: "\e907";
    color: #fff;
  }
  .icon-icon-youtube:before {
    content: "\e908";
    color: #fff;
  }
  .icon-Icon-search:before {
    content: "\e909";
    color: #fff;
}