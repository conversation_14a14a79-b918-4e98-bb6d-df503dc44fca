.e-services {
    width: 100%;
    @media(max-width:767px) {
        .container {
          max-width: 100%;
        }
    }
    &__content {
        width: 100%;
        form {
            background: none;
            box-shadow: none;
            border-radius: 0;
            padding: 0;
        }
        img {
            transform: scale(1) !important;
        }
        .tabs {
            display: flex;
            margin-left: auto;
            margin-right: auto;
            width: max-content;
            outline: 1px solid $primary;
            outline-offset: 4px;
            border-radius: 27px;
            gap: 20px;
            margin-bottom: 0;
            li, &>div {
                font-family: $rubik-bold;
                font-weight: bold;
                border-radius: 24px;
                padding: 6px 20px;
                display: flex;
                align-items: center;
                text-transform: uppercase;
                color: $border;
                font-size: rem(15);
                cursor: pointer;
                &.active {
                    background: $primary;
                    color: $white;
                }
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                }
            }
            @media only screen and (max-width: 480px) {
                width: 100%;
                justify-content: space-between;
            }
        }
        #tab1 {
            position: relative;
            padding: 0 rem(70);
            margin: 0 auto;
            @media(max-width:768px) {
                padding: 0;
            }
        }
        .tab_container {
            .tab_content {
                display: none;
            }
        }

        .swiper-services {
            border-radius: 15px;
            padding: rem(30) 0;
            position: initial;
            padding-bottom: 40px;
            @media only screen and (max-width: 600px) {
                padding: 30px 0;
                // margin-bottom: rem(30);
                margin-bottom: 0;
            }
            .items {
                .item {
                    padding: rem(50) rem(20) rem(50) rem(40);
                    @media(max-width:600px) {
                        padding: rem(25)
                    }
                }
            }
            .swiper-slide {
                background: rgba($black, .04);
                border-radius: 15px;
                transition: all 0.3s ease;
                height: initial;
                box-shadow: 0 10px 12px rgba(#436597, .05);
                @media only screen and (max-width: 481px) {
                    background: $white;
                    border-radius: 0;
                    border-radius: 15px;
                }
                h3 {
                    font-family: $rubik-bold;
                    font-weight: bold;
                    font-size: rem(18);
                    color: $black;
                    margin-bottom: 8px;
                    line-height: 1.4;
                    padding-top: rem(15);
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                    }
                }
                p {
                    font-family: $quicksand-regular;
                    font-size: rem(16);
                    padding: rem(10) 0;
                    line-height: 1.2;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                        font-weight: 400;
                    }
                }
                .btn {
                    font-size: rem(14);
                    // padding: rem(8) rem(30) rem(8) rem(20);
                    padding-top: 10px;
                    padding-bottom: 10px;
                    z-index: 2;
                }
                &.swiper-slide-active {
                    background: $white;
                    border-radius: 15px;
                    z-index: 2;
                    transform: scale(1.1);
                    // box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
                    animation: pulse 1.5s ease-in-out;
                    .item {
                        zoom: .95;
                    }
                    @media only screen and (max-width: 768px) {
                        transform: scale(1);
                        animation: none;
                        .item {
                           zoom: 1;
                        }
                        
                    }
                    
                }
            }

            //Button
            .swiper-button-prev, .swiper-rtl .swiper-button-next,
            .swiper-button-next, .swiper-rtl .swiper-button-prev {
                &:after {
                    content: "";
                    position: absolute;
                    mask: url($images-path + 'icons/icon-arrow-circle.svg') no-repeat 0 0;
                    width: 39px;
                    height: 39px;
                    background: $black;
                    transition: all 450ms ease;
                }
                &:hover {
                    &:after {
                        background: $secondary;
                    }
                }
                @media only screen and (max-width: 640px) {
                    display: none;
                }
            }
            .swiper-button-prev, .swiper-rtl .swiper-button-next {
                // left: 0;
                @include start(position, 0);
            }
            .swiper-button-next, .swiper-rtl .swiper-button-prev {
                // right: 0;
                @include end(position, 0);
                transform: rotate(180deg);
                
            }
        }
    }

    &.e-services-wysiw {
        .e-services__content {
            img {
                width: 100%;
                margin: 0;
                padding: 0;
            }
            #tab1 {
                position: relative;
                padding: 0 rem(70);
                margin: 0 auto;
                @media(max-width:768px) {
                    padding: 0;
                }
            }
            .swiper-services {
                border-radius: 15px;
                padding: rem(30) 0;
                position: initial;
                padding-bottom: 40px;
                @media only screen and (max-width: 600px) {
                    padding: 30px 0;
                    margin-bottom: rem(30);
                }
                .items {
                    .item {
                        padding: 0;
                    }
                }
                .swiper-slide {
                    background: transparent;
                    border-radius: 15px;
                    transition: all 0.3s ease;
                    height: initial;
                    box-shadow: 0 10px 12px rgba(#436597, .05);
                    @media only screen and (max-width: 481px) {
                        background: $white;
                        border-radius: 0;
                    }
                    h3 {
                        font-family: $rubik-bold;
                        font-weight: bold;
                        font-size: rem(18);
                        color: $black;
                        margin-bottom: 8px;
                        line-height: 1.4;
                        padding-top: rem(15);
                        html[dir="rtl"] & {
                            font-family: "Cairo", sans-serif;
                        }
                    }
                    p {
                        font-family: $quicksand-regular;
                        font-size: rem(16);
                        padding: rem(10) 0;
                        line-height: 1.2;
                        html[dir="rtl"] & {
                            font-family: "Cairo", sans-serif;
                            font-weight: 400;
                        }
                    }
                    .btn {
                        font-size: rem(14);
                        padding-top: 10px;
                        padding-bottom: 10px;
                        z-index: 2;
                    }
                    &.swiper-slide-active {
                        background: transparent;
                        border-radius: 15px;
                        z-index: 2;
                        transform: scale(1.1);
                        // box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
                        animation: pulse 1.5s ease-in-out;
                        .item {
                            zoom: 1;
                        }
                        @media only screen and (max-width: 768px) {
                            transform: scale(1);
                            animation: none;
                            .item {
                               zoom: 1;
                            }
                            
                        }
                        
                    }
                }
    
                //Button
                .swiper-button-prev, .swiper-rtl .swiper-button-next,
                .swiper-button-next, .swiper-rtl .swiper-button-prev {
                    &:after {
                        content: "";
                        position: absolute;
                        mask: url($images-path + 'icons/icon-arrow-circle.svg') no-repeat 0 0;
                        width: 39px;
                        height: 39px;
                        background: $black;
                        transition: all 450ms ease;
                    }
                    &:hover {
                        &:after {
                            background: $secondary;
                        }
                    }
                    @media only screen and (max-width: 640px) {
                        display: none;
                    }
                }
                .swiper-button-prev, .swiper-rtl .swiper-button-next {
                    // left: 0;
                    @include start(position, 0);
                }
                .swiper-button-next, .swiper-rtl .swiper-button-prev {
                    // right: 0;
                    @include end(position, 0);
                    transform: rotate(180deg);
                    
                }
            }
        }
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1.1);
    }
}


