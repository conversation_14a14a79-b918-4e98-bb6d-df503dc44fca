.thumbsSlider-secteur {
    .onglets {
        .swiper-onglet {
            .swiper-wrapper {
                justify-content: space-between;
            }
            .swiper-slide {
                width: 100% !important;
                flex: 1;
                & > div {
                    display: flex;
                    width: calc(100% - 20px);
                    height: 100%;
                }
                a {
                    font-family: $rubik-bold;
                    font-weight: bold;
                    width: 100%;
                    align-items: center;
                    display: flex;
                    // padding-left: 25px;
                    @include start(padding, rem(25));
                    justify-content: space-between;
                    background: $white;
                    box-shadow: 0 10px 12px rgba(#426297, .05);
                    border-radius: 15px;
                    color: rgba($black, .6);
                    font-size: 18px;
                    text-transform: none;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                    }
                    .fa-arrow-right {
                        animation: none;
                        &:before {
                            font-size: 28px;
                        }
                    }
                    &:hover {
                        background: $primary;
                        color: $white; 
                    }
                }
                &.swiper-slide-thumb-active {
                    a {
                        background: $primary;
                        color: $white;
                    }
                }
                &:last-child {
                    margin-bottom: 0 !important;
                }
                @media(max-width: 767px) {
                    flex: none;
                    & > div {
                        width: 75%;
                        margin: 0 auto 20px;
                    }
                }
            }//Button
            .swiper-button-prev, .swiper-rtl .swiper-button-next,
            .swiper-button-next, .swiper-rtl .swiper-button-prev {
                display: none;
                @media(max-width: 767px) {
                    display: block;
                }
                width: 39px;
                height: 39px;
                &:after {
                    content: "";
                    position: absolute;
                    mask: url($images-path + 'icons/icon-arrow-circle.svg') no-repeat 0 0;
                    width: 39px;
                    height: 39px;
                    background: $primary;
                }
                &:hover {
                    &:after {
                        background: $secondary;
                    }
                }
                
            }
            .swiper-button-prev, .swiper-rtl .swiper-button-next {
                left: 0;
            }
            .swiper-button-next, .swiper-rtl .swiper-button-prev {
                right: 0;
                transform: rotate(180deg);
            }
        }
        .contentsOnglet {
            .swiper-slide {
                .picture {
                    display: flex;
                    height: 100%;
                    img {
                        width: 100%;
                        object-fit: cover;
                        border-radius: 15px;
                        filter: brightness(40%);
                    }
                }
                .content {
                    position: absolute;
                    width: auto;
                    height: 64px;
                    bottom: 38px;
                    margin-left: rem(40);
                    margin-right: rem(10);
                    transition: all 1000ms cubic-bezier(0.18, 0.89, 0.32, 1.28);
                    z-index: 2;
                    overflow: hidden;
                    cursor: pointer;
                    & > div {
                        overflow: hidden;
                        height: 0;
                        bottom: 100%;
                        @include end(padding, 20px);
                    }
                    p {
                        padding: 10px 0 20px;
                        color: $white;
                    }
                }
                &:hover {
                    .content {
                        height: auto;
                        bottom: 10%;
                        overflow: visible;
                        transform: translateY(0%);
                        & > div {
                            overflow: visible;
                            height: auto;
                        }
                    }
                }
                @media(max-width: 767px) {
                    .picture {
                        img {
                           height: 350px;
                        }
                    }
                    .content {
                        height: auto;
                        bottom: 10%;
                        overflow: visible;
                        transform: translateY(0%);
                        & > div {
                            overflow: visible;
                            height: auto;
                        }
                    }
                }
            }
        }
        // @include swiper-dots;
    }
    
}