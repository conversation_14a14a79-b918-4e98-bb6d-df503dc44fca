.projet {
    width: 100%;
    @media(max-width:767px) {
        .container{
          max-width: 100%;
        }
    }
    &__items {
        width: 100%;
        &--item {
            position: relative;
            &::before {
                content: "";
                position: absolute;
                inset: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1;
                border-radius: 14px;
            }
            .picture {
                border-radius: 14px;
            }
            img {
                width: 100%;
                height: 100%;
            }
            .content {
                position: absolute;
                width: auto;
                height: 64px;
                bottom: 38px;
                @include start(padding, rem(40));
                @include end(margin, rem(10));
                transition: all 800ms cubic-bezier(0.18, 0.89, 0.32, 1.28);
                z-index: 2;
                overflow: hidden;
                cursor: pointer;
                a {
                    position: relative;
                    z-index: 99;
                }
                &.content-region {
                    height: 82px;
                }
                & > div {
                    overflow: hidden;
                    height: 0;
                }

                p {
                    padding: 10px 0 20px;
                    color: $white;
                }
                @media (max-width:768px) {
                    height: auto;
                    bottom: 10%;
                    overflow: visible;
                    transform: translateY(0%);
                    zoom: .9;
                    & > div {
                        overflow: visible;
                        height: auto;
                    }
                }
                @media (max-width:480px) {
                    zoom: .8;
                }
                @media (max-width:320px) {
                    zoom: .7;
                }
            }

            &:hover {
                .content {
                    height: auto;
                    bottom: 10%;
                    overflow: visible;
                    transform: translateY(0%);
                    & > div {
                        overflow: visible;
                        height: auto;
                    }
                }
            }
        }
    }
    @include container-slide;
}