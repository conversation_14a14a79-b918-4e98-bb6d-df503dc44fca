.publication {
    width: 100%;
    .card {
        height: 100%;
        padding: 30px;
        img {
            width: 102px;
            height: 123px;
            border-radius: 0;
        }
        .card-body {
            padding: 20px 0 0 0;
            a.btn {
                font-family: $quicksand-regular;
                color: $primary;
                margin-bottom: 20px;
                background-color: rgba($primary, .1);
                border-color: transparent;
                font-size: rem(16);
                text-transform: none;
                padding: 10px 20px;
                pointer-events: none;
                 html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 400;
                }
            }
            p {
                font-family: $rubik-bold;
                font-weight: bold;
                font-size: 18px;
                margin-bottom: rem(30);
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                }
            }
        }
    }
}