//modif
.presentation {
    width: 100%;
    // padding-bottom: 40px;
    // padding-top: 60px;
    h2 {
        // margin-bottom: 10px;
        padding-bottom: 0;

    }
    .card-body {
        padding-left: 35px;
        padding-right: 35px;
    }
    .content {
        width: 100%;
    }
    .tabs-button {
      position: fixed;
      // right: 0;
      @include end(position, 0px);
      transform: translateX(150%);
      top: 22%;
      padding: 20px;
      align-items: end;
      width: 64px;
      z-index: 1029;
      box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
      transition: all 600ms cubic-bezier(0.18, 0.89, 0.32, 1.28);
      // padding-right: 2px;
      @include end(padding, 2px);
      a {
          font-family: $rubik-regular;
          position: relative;
          font-size: rem(14);
          color: rgba($black, .7);
          background: transparent;
          box-shadow: none;
          border: 0;
          text-transform: none;
          transition: font-size 450ms ease, width 450ms ease;
          text-indent: -9999px;
          
          &:after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            // right: 20px;
            @include end(position, 20px);
            top: 50%;
            transform: translateY(-50%);
            border-radius: 100%;
            border: 1px solid $primary;
            transition: all 250ms ease;
          }
          &:hover {
            color: $primary;
            background-color: #eaf0f9;
            &:after {
              background: $primary;
            }
          }
          &.active {
            &:after {
              background: $primary;
            }
          }
          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
          }
      }
      &.open {
        width: auto;
        padding-right: 10px;
        padding-left: 10px;
        a {
          text-indent: inherit;
          &:after {
            // right: 20px;
            @include end(position, 20px);
          }
        }
      }
      .window_scroll & {
        transform: translateX(0);
        // right: 20px;
        @include end(position, 20px);
      }
      @media (max-width: 992px) {
        display: none;
      }
    }
    .presentation {
        display: flex;
        gap: 20px;
        & > div {
            flex: 1;
        }
        &-left {
            .card-body {
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: 100%;
            }
        }
        .has-tabs-button & {
          // padding: 0;
          // margin-top: 0 !important;
          .container {
            margin-top: 0 !important;
          }
        }
    }
    .card {
        &.mission {
            box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
        }
    }
}
//scroll
.scroll-wrapper {
    padding-top: 70px;
  }
  /* En dessous de 992px */
  @media (max-width: 992px) {
    .scroll-wrapper {
      overflow-x: scroll;
      scroll-snap-type: x mandatory; /* Activé le scroll snap horizontal */
      margin: 0 auto;
      padding-bottom: 20px;
    }
    .scroll-wrapper::-webkit-scrollbar {
      height: 4px;
    }
  
    .scroll-wrapper::-webkit-scrollbar-track {
      background: transparent;
    }
  
    .scroll-wrapper::-webkit-scrollbar-thumb {
      background-color: #3C77CE;
      border-radius: 4px;
    }
  
    .scroll-wrapper::-webkit-scrollbar-thumb:hover {
      background: #17BBCE;
    }
    .organigrame__membres {
      display: flex;
      flex-wrap: nowrap;
      min-width: max-content;
    }
  }