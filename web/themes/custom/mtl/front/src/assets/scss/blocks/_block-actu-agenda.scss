.itemsWrapper {
    width: 100%;
    // margin-bottom: 50px;
    // padding-top: 60px;
    @media(max-width:767px) {
        .container {
          max-width: 100%;
        }
    }
    
    .lune {
        .card {
            height: 100%;
            h3 {
                max-width: 490px;
                font-size: rem(17);
                margin-bottom: rem(5);
                text-transform: none;
            }
            p {
                // padding-right: 50px;
                @include end(padding, rem(50));
                @media(max-width:480px) {
                    @include end(padding, 0);
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .card-body {
                padding: 20px 24px;
            }
            img {
                // height: 100%;
                height: 352px;
                object-fit: cover;
            }
        }
        div[data-once="ajax-pager"] {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    }
    .bloc-agenda {
        .card {
            z-index: 0;
            min-height: 150px;
            img {
                width: 67px;
                height: 67px;
            }
            @media (max-width: 992px) {
                padding-left: 15px;
                zoom: .9;
            }
            @media (max-width:768px) {
                padding: 30px 30px 30px 20px;
                .card-body {
                    container-type: inline-size;
                }
                p {
                    font-size: rem(16);
                }
            }
            @media (max-width:480px) {
                max-height: 130px;
                p.card-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                p.para, p.card-text {
                    font-size: 14px;
                    span {
                        font-size: 14px;
                    }
                }
            }   
            @media (max-width: 376px) {
                p.para {
                    gap: 5px !important;
                    zoom: .9;
                    span {
                        font-size: 14px;

                    }
                }
            }
        }
        .btn-wrapper {
            justify-content: flex-end;
            @media(max-width: 768px) {
                justify-content: center;
            }
        }
        .swiper-bloc-agenda .swiper-wrapper {
            flex-direction: column;
            @media(max-width:640px) {
                flex-direction: row;
                .swiper-slide {
                    height: auto;
                    .card {
                        height: 100%;
                        min-height: inherit;
                        max-height: 150px;
                    }
                }
            }
        }
    }
}

.agenda {
    .bloc-agenda {
        .card {
            .calendar {
                border-color: $primary;
                &:before {
                    background-color: $primary;
                }
                span {
                    color: $primary;
                }
                span.tree {
                    background: $primary;
                    &:before, &:after {
                        background: $primary;
                    }
                }
            }
        }
    }
}