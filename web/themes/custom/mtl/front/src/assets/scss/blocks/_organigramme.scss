.organigrame {
    width: 100%;
    h2 {
        padding-bottom: 18px;
    }
    &__membres {
        display: flex;
        flex-direction: column;
        gap: 50px;

    .card {
        border-radius: 10px;
        position: relative;
        background: #e2edfa;
        flex: 1;
        // padding: 70px 40px 0px 40px;
        &:after,
        &:before {
            content: "";
            position: absolute;
            background: $black;
            left: 50%;
            // @include start(position, 50%);
            top: -20px;
        }
        &:after {
            width: .8px;
            height: 20px;
        }
        &:before {
            width: 120%;
            height: .8px;
            transform: translateX(-50%);
        }
        p {
            text-align: center;
            margin-bottom: 0;
            span {
                font-family: $rubik-bold;
                font-weight: bold;
                color: $primary;
                display: block;
                margin-bottom: 10px;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 700;
                }
            }
            cite {
                font-family: $quicksand-bold;
                position: absolute;
                position: relative;
                // width: 80%;
                width: 100%;
                // left: 50%;
                // transform: translateX(-50%);
                // bottom: 20px;
                font-style: normal;
                line-height: 1.2;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 700;
                }
            }
        }
        .card-body {
            // padding: 25px 10px;
            padding: 25px 0;
        }
        .picture {
            width: 150px;
            position: absolute;
            top: -70px;
            left: 50%;
            // @include start(position, 50%);
            transform: translateX(-50%);
        }
    }
    &--one,
    &--two,
    &--tree{
        display: flex;
        justify-content: center;
        gap: 20px;
    }
    &--one {
        .card {
            // flex: inherit;
            width: 100%;
            max-width: 340px;
            padding: 70px 30px 10px 30px;
            .picture {
                width: 150px;
                position: absolute;
                top: -70px;
                left: 50%;
                // @include start(position, 50%);
                transform: translateX(-50%);
            }
            &::before {
                display: none;
            }
            &:after {
                top: auto;
                bottom: -50px;
                height: 50px;
            }
        }
    }
    &--two {
        .card {
            &:nth-child(2) {
                &:after {
                    width: .8px;
                    height: 30px;
                    // left: 50%;
                    @include start(position, 50%);
                    bottom: -30px;
                    top: auto;
                }
            }
        }
    }
    &--two,&--tree {
        .card {
            &:first-child ,
            &:last-child {
                &::before {
                    width: 50%;
                    // left: 50%;
                    @include start(position, 50%);
                }
            }
            &:first-child {
                &:before {
                    transform: translateX(0);
                }
            }
            &:last-child {
                &:before {
                    transform: translateX(-100%);
                }
            }
        }
    }
    &--tree {
            .card {
                padding-bottom: 60px;
            }
        }
    }
    &.organisation {
        .organigrame__membres {
            gap: 120px;
        }
        .card {
            width: 100%;
            max-width: 340px;
            // padding: 70px 30px 10px 30px;
            padding: 70px 10px 10px 10px;
            &:after,
            &:before {
                top: -90px;
            }
            .picture {
                img {
                    padding: 0 !important;
                }
            }
            p {
                line-height: 1.6 !important;
            }
        }
        .organigrame__membres--one {
            .card {
                &::after {
                    top: auto;
                    bottom: -50px;
                    height: 50px;
                }
            }
        }
        .organigrame__membres--two {
            .card {
                &:nth-child(2) {
                    &:after {
                        height: 30px;
                        top: auto;
                    }
                }
            }
        }
        .organigrame__membres--four {
            display: grid;
            grid-template-columns: repeat(6,1fr);
            gap: 20px;
            .card {
                &:nth-child(1) {
                    &::before {
                        transform: translateX(0);
                        width: calc(50% + 10px)
                    } 
                    &::after {
                        top: -120px;
                        height: 50px;
                    }

                }
            
                // &:before {
                //     // display: none;
                //     transform: translateX(0);
                //     width: 110%;
                // }
                &:nth-child(2) {
                    &::before {
                        transform: translateX(-100%);
                        width: calc(50% + 10px)
                    }
                }
            }
        }
    }
}