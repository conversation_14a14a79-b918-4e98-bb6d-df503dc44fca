// -----------------------------------------------------------------------------
// This file contains all styles related to the button component.
// -----------------------------------------------------------------------------

//***************** Animation link *************************
.animation-link {
    a {
        position: relative;
        transition: all 350ms ease-in;
        &:after {
            content: "";
            position: absolute;
            bottom: -3px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 0;
            height: 1px;
            transition: all 450ms ease-in;
        }
        &:hover {
            &:after {
                width: 100%;
            }
        }
    }
}

.bold {
    font-family: $rubik-bold;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
        font-weight: bold;
    }
}
.btn {
    border-radius: 24px !important;
    text-transform: uppercase;
    transition: all 450ms ease-in;
    padding: 14px 45px;
    box-shadow: none;
    font-size: rem(14);
    &.lowercase {
        text-transform: none;
    }
    i {
        // padding-left: 4px;
        @include start(padding, 4px);
    }
    &.btn-outline-primary {
        border-color: $primary;
        // color: $white;
        &:hover {
            background: $primary;
            color: $white;
        }
    }
    &.btn-outline-success {
        border-color: $secondary;
        color: $secondary;
        &:hover {
            background-color: $secondary;
            border-color: $secondary;
            color: $white;
        }
    }
    &.btn-success {
        background-color: $secondary;
        box-shadow: none;
        border-color: $secondary;
        &:hover {
            background-color: $primary;
            border-color: $primary;
        }
    }
    &.btn-primary {
        background-color: $primary;
        box-shadow: none;
        border-color: $primary;
        &:hover {
            background-color: transparent;
            color: $primary;
        }
    }
    @media(max-width:768px) {
        font-size: clamp(12px, 4vw, 14px);
        padding: 10px 30px;
    }
    &.size {
        min-width: 258px;
    }
}
// @keyframes slide-in-left {
//     0% {
//       transform: translateX(-8px);
//       opacity: 0;
//     }
  
//     100% {
//       transform: translateX(0px);
//       opacity: 1;
//     }
// }
//Style wraper-btn
