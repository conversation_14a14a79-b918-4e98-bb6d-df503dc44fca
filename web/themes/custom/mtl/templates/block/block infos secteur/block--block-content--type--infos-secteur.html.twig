{#
/**
 * @file
 * Default theme implementation to display a block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 *   - label: The configured label for the block.
 *   - label_display: The display settings for the label.
 *   - provider: The module or other provider that provided this block plugin.
 *   - Block plugin specific settings will also be stored here.
 * - in_preview: Whether the plugin is being rendered in preview mode.
 * - content: The content of this block.
 * - attributes: array of HTML attributes populated by modules, intended to
 *   be added to the main container tag of this template.
 *   - id: A valid HTML ID and guaranteed unique.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_block()
 *
 * @ingroup themeable
 */
#}

{{ title_prefix }}
{% if label %}
  {{ label }}
{% endif %}
{{ title_suffix }}
{% block content %}
<section class="thumbsSlider-secteur margeBlock">
        <div class="onglets container">
            <div class="container">
                <div class="row g-3">
                    <div thumbsSlider="" class="swiper swiper-onglet mySwiper col-md-4">
                        <div class="swiper-wrapper">
                            {{ content.field_tabs_infos_secteur }}
                        </div>
                        <div class="navigation-swiper">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                    <div class="swiper contentsOnglet mySwiper2 col-md-8">
                        <div class="swiper-wrapper">
                        {{ content.field_infos_secteur }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section>
{% endblock %}