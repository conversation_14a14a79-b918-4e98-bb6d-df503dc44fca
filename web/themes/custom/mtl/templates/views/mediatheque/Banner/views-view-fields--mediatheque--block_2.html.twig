{#
/**
 * @file
 * Default theme implementation for main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 *
 * @ingroup themeable
 */
#}

{# Lien de l'image #}
{% set image_uri = row._entity.field_media.entity.field_media_image.entity.uri.value %}
{# Alt de l'image #}
{% set image_alt = row._entity.field_media.entity.field_media_image.alt %}
{# Lien de la video #}
{% set video_uri = row._entity.field_video.entity.field_media_video_file.entity.uri.value %}
{# <pre>
  {{ dump(row._entity.field_video.entity.field_media_video_file.entity.uri.value) }}
</pre> #}
{% if video_uri %}
  <div class="video-wrapper">
    <video autoplay="true" loop preload="auto" muted playsinline>
      <source src="{{ file_url(video_uri) }}" type="video/mp4">
    </video>
  </div>
{% else %}
  <img src="{{ file_url(image_uri|image_style('1400_x_656'))}}" alt="{{ image_alt }}" fetchpriority="high" width="1400" height="656" loading="eager">
{% endif %}