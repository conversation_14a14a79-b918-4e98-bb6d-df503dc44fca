{#
/**
 * @file
 * Theme override to display a menu.
 *
 * Available variables:
 * - menu_name: The machine name of the menu.
 * - items: A nested list of menu items. Each menu item contains:
 *   - attributes: HTML attributes for the menu item.
 *   - content.children: The menu item child items.
 *   - title: The menu link title.
 *   - url: The menu link url, instance of \Drupal\Core\Url
 *   - content: The field item content.
 *   - localized_options: Menu link localized options.
 *   - is_expanded: TRUE if the link has visible children within the current
 *     menu tree.
 *   - is_collapsed: TRUE if the link has children within the current menu tree
 *     that are not currently visible.
 *   - in_active_trail: TRUE if the link is in the active trail.
 */
#}

{% import _self as menu %}

{{ menu.menu_links(items, attributes, 0) }}

{% macro menu_links(items, attributes, menu_level) %}
  <ul>
    {% for key, item in items %}
      {% if (key|first) != '#' %}
        {% set menu_item_classes = ['menu-item', item.is_expanded ? 'menu-item--expanded', item.is_collapsed ? 'menu-item--collapsed', item.in_active_trail ? 'menu-item--active-trail'] %}

        <li>
          <div class="card">
            {% if item.content.field_image %}
              <img class="card-img-top" src="{{ file_url(item.content.field_image[0]['#item'].entity.uri.value) }}" alt="{{ item.content.field_image[0]['#item'].alt }}" class="card-img-top" />
            {% endif %}
            <div class="card-body">
              {% if item.url.routeName == '<nolink>' %}
                <span class="card-title">{{ item.title }}</span>
              {% else %}
                <a href="{{ item.url }}"><span class="card-title">{{ item.title }}</span></a>
              {% endif %}
              {% if item.below %}
                <div>
                  {% for sous_lien in item.below %}
                    {% if sous_lien.url %}
                      <a href="{{ sous_lien.url }}" class="card-link" style="z-index: 1000;">{{ sous_lien.title }}</a>
                    {% endif %}
                  {% endfor %}
                </div>
              {% else %}
                {# <h5 class="card-title">{{item.title}}</h5> #}
                <a href="{{ item.url }}" class="click-me"></a>
              {% endif %}
            </div>
          </div>
        </li>
      {% endif %}
    {% endfor %}
  </ul>
{% endmacro %}
