{#
/**
 * @file
 * Default theme implementation to display a node.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - node.getCreatedTime() will return the node creation timestamp.
 *   - node.hasField('field_example') returns TRUE if the node bundle includes
 *     field_example. (This does not indicate the presence of a value in this
 *     field.)
 *   - node.isPublished() will return whether the node is published or not.
 *   Calling other methods, such as node.delete(), will result in an exception.
 *   See \Drupal\node\Entity\Node for a full list of public properties and
 *   methods for the node object.
 * - label: (optional) The title of the node.
 * - content: All node items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - author_picture: The node author user entity, rendered using the "compact"
 *   view mode.
 * - metadata: Metadata for this node.
 * - date: (optional) Themed creation date field.
 * - author_name: (optional) Themed author name field.
 * - url: Direct URL of the current node.
 * - display_submitted: Whether submission information should be displayed.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - node: The current template type (also known as a "theming hook").
 *   - node--type-[type]: The current node type. For example, if the node is an
 *     "Article" it would result in "node--type-article". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - node--view-mode-[view_mode]: The View Mode of the node; for example, a
 *     teaser would result in: "node--view-mode-teaser", and
 *     full: "node--view-mode-full".
 *   The following are controlled through the node publishing options.
 *   - node--promoted: Appears on nodes promoted to the front page.
 *   - node--sticky: Appears on nodes ordered above other non-sticky nodes in
 *     teaser listings.
 *   - node--unpublished: Appears on unpublished nodes visible only to site
 *     admins.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main
 *   content tag that appears in the template.
 * - author_attributes: Same as attributes, except applied to the author of
 *   the node tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - teaser: Flag for the teaser state. Will be true if view_mode is 'teaser'.
 * - page: Flag for the full page state. Will be true if view_mode is 'full'.
 * - readmore: Flag for more state. Will be true if the teaser content of the
 *   node cannot hold the main body content.
 * - logged_in: Flag for authenticated user status. Will be true when the
 *   current user is a logged-in member.
 * - is_admin: Flag for admin user status. Will be true when the current user
 *   is an administrator.
 *
 * @see template_preprocess_node()
 *
 * @ingroup themeable
 */
#}
<div class="m-organigramme">
	<div class="container">
		<div id="c-wysiwyg">
			{{ content }}
		</div>
	</div>
</div>

<div class="margeBlock">
	<div class="container share-buttons">
		<h2 class="h2-title h3-title transform-none pb-2">{{ "Partager cette page"|t }}</h2>
		<div class="share-links d-flex flex-wrap gap-2 a2a_kit a2a_kit_size_32 a2a_default_style">
			<!-- Links -->
			<a href="/#facebook" class="btn btn-outline-secondary p-2 a2a_button_facebook" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur Facebook" data-bs-original-title="{{ 'Share on Facebook'|t }}" target="_blank" rel="nofollow noopener">
				<i class="fab fa-facebook-f"></i>
			</a>
			<a href="/#whatsapp" class="btn btn-outline-secondary p-2 a2a_button_whatsapp" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur whatsapp" data-bs-original-title="{{ 'Share on whatsapp'|t }}" target="_blank" rel="nofollow noopener">
				<i class="fa-brands fa-whatsapp"></i>
			</a>
			<a href="/#linkedin" class="btn btn-outline-secondary p-2 a2a_button_linkedin" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur Linkedin" data-bs-original-title="{{ 'Share on Linkedin'|t }}" target="_blank" rel="nofollow noopener">
				<i class="fa-brands fa-linkedin-in"></i>
			</a>
			<a href="/#x" class="btn btn-outline-secondary p-2 a2a_button_x" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur x" data-bs-original-title="{{ 'Share on X'|t }}" target="_blank" rel="nofollow noopener">
				<i class="fa-brands fa-x-twitter"></i>
			</a>
			<a href="/#x" class="btn btn-outline-secondary p-2 a2a_button_email" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur email" data-bs-original-title="{{ 'Share on Email'|t }}" target="_blank" rel="nofollow noopener">
				<i class="fa-brands fa-google"></i>
			</a>
			<a href="#" class="btn btn-outline-secondary p-2 a2a_button_print" data-toggle="tooltip" data-placement="top" aria-label="click to print" data-bs-original-title="{{ 'Click to print'|t }}">
				<i class="fas fa-print"></i>
			</a>
			<a href="#" class="btn btn-outline-secondary p-2 a2a_dd" data-toggle="tooltip" data-placement="top" aria-label="click to share">
				<i class="fas fa-plus"></i>
			</a>
			<div style="clear: both;"></div>
		</div>
	</div>
</div>