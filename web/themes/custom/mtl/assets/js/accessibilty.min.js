"use strict";
(function ($) {(function(e){function s(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}var a={};return s.m=e,s.c=a,s.d=function(e,a,t){s.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:t})},s.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},s.t=function(e,a){if(1&a&&(e=s(e)),8&a)return e;if(4&a&&'object'==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(s.r(t),Object.defineProperty(t,'default',{enumerable:!0,value:e}),2&a&&'string'!=typeof e)for(var n in e)s.d(t,n,function(s){return e[s]}.bind(null,n));return t},s.n=function(e){var a=e&&e.__esModule?function(){return e['default']}:function(){return e};return s.d(a,'a',a),a},s.o=function(e,s){return Object.prototype.hasOwnProperty.call(e,s)},s.p='',s(s.s=4)})([function(e){(function(s,a){e.exports=a()})(this,function(){'use strict';function e(e){for(var s,a=1;a<arguments.length;a++)for(var t in s=arguments[a],s)e[t]=s[t];return e}function s(a,t){function n(s,n,l){if('undefined'!=typeof document){l=e({},t,l),'number'==typeof l.expires&&(l.expires=new Date(Date.now()+864e5*l.expires)),l.expires&&(l.expires=l.expires.toUTCString()),s=encodeURIComponent(s).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i='';for(var c in l)l[c]&&(i+='; '+c,!0!==l[c])&&(i+='='+l[c].split(';')[0]);return document.cookie=s+'='+a.write(n,s)+i}}return Object.create({set:n,get:function(e){if('undefined'!=typeof document&&(!arguments.length||e)){for(var s=document.cookie?document.cookie.split('; '):[],t={},n=0;n<s.length;n++){var l=s[n].split('='),i=l.slice(1).join('=');try{var c=decodeURIComponent(l[0]);if(t[c]=a.read(i,c),e===c)break}catch(s){}}return e?t[e]:t}},remove:function(s,a){n(s,'',e({},a,{expires:-1}))},withAttributes:function(a){return s(this.converter,e({},this.attributes,a))},withConverter:function(a){return s(e({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(a)}})}var a=s({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:'/'});return a});/*! js-cookie v3.0.5 | MIT */},function(e,s,a){'use strict';function t(e,s){if(!(e instanceof s))throw new TypeError('Cannot call a class as a function')}function n(e,s){for(var a,t=0;t<s.length;t++)a=s[t],a.enumerable=a.enumerable||!1,a.configurable=!0,'value'in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}function l(e,s,a){return s&&n(e.prototype,s),a&&n(e,a),e}Object.defineProperty(s,'__esModule',{value:!0}),s.default=void 0;var i=a(5),c=function(){function e(){t(this,e),this.browserSupport='speechSynthesis'in window&&'SpeechSynthesisUtterance'in window,this.synthesisVoice=null}return l(e,[{key:'init',value:function(){var e=this,s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(a,t){e.browserSupport||t('Your browser does not support Speech Synthesis');var n=(0,i.isNil)(s.listeners)?{}:s.listeners,l=!!(0,i.isNil)(s.splitSentences)||s.splitSentences,c=(0,i.isNil)(s.lang)?void 0:s.lang,r=(0,i.isNil)(s.volume)?1:s.volume,o=(0,i.isNil)(s.rate)?1:s.rate,d=(0,i.isNil)(s.pitch)?1:s.pitch,p=(0,i.isNil)(s.voice)?void 0:s.voice;Object.keys(n).forEach(function(e){var s=n[e];'onvoiceschanged'!==e&&(speechSynthesis[e]=function(e){s&&s(e)})}),e._loadVoices().then(function(s){n.onvoiceschanged&&n.onvoiceschanged(s),(0,i.isNil)(c)||e.setLanguage(c),(0,i.isNil)(p)||e.setVoice(p),(0,i.isNil)(r)||e.setVolume(r),(0,i.isNil)(o)||e.setRate(o),(0,i.isNil)(d)||e.setPitch(d),(0,i.isNil)(l)||e.setSplitSentences(l),a({voices:s,lang:e.lang,voice:e.voice,volume:e.volume,rate:e.rate,pitch:e.pitch,splitSentences:e.splitSentences,browserSupport:e.browserSupport})}).catch(function(s){t(s)})})}},{key:'_fetchVoices',value:function(){return new Promise(function(e,s){setTimeout(function(){var a=speechSynthesis.getVoices();return 0<(0,i.size)(a)?e(a):s('Could not fetch voices')},100)})}},{key:'_loadVoices',value:function(){var e=this,s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:10;return this._fetchVoices().catch(function(a){if(0===s)throw a;return e._loadVoices(s-1)})}},{key:'hasBrowserSupport',value:function(){return this.browserSupport}},{key:'setVoice',value:function(e){var s,a=speechSynthesis.getVoices();if((0,i.isString)(e)&&(s=a.find(function(s){return s.name===e})),(0,i.isObject)(e)&&(s=e),s)this.synthesisVoice=s;else throw'Error setting voice. The voice you passed is not valid or the voices have not been loaded yet.'}},{key:'setLanguage',value:function(e){if(e=e.replace('_','-'),(0,i.validateLocale)(e))this.lang=e;else throw'Error setting language. Please verify your locale is BCP47 format (http://schneegans.de/lv/?tags=es-FR&format=text)'}},{key:'setVolume',value:function(e){if(e=parseFloat(e),!(0,i.isNan)(e)&&0<=e&&1>=e)this.volume=e;else throw'Error setting volume. Please verify your volume value is a number between 0 and 1.'}},{key:'setRate',value:function(e){if(e=parseFloat(e),!(0,i.isNan)(e)&&0<=e&&10>=e)this.rate=e;else throw'Error setting rate. Please verify your volume value is a number between 0 and 10.'}},{key:'setPitch',value:function(e){if(e=parseFloat(e),!(0,i.isNan)(e)&&0<=e&&2>=e)this.pitch=e;else throw'Error setting pitch. Please verify your pitch value is a number between 0 and 2.'}},{key:'setSplitSentences',value:function(e){this.splitSentences=e}},{key:'speak',value:function(e){var s=this;return new Promise(function(a,t){var n=e.text,l=e.listeners,c=void 0===l?{}:l,r=e.queue,o=(0,i.trim)(n);(0,i.isNil)(o)&&a(),void 0===r||r||s.cancel();var d=[],p=s.splitSentences?(0,i.splitSentences)(o):[o];p.forEach(function(e,n){var l=n===(0,i.size)(p)-1,r=new SpeechSynthesisUtterance;s.synthesisVoice&&(r.voice=s.synthesisVoice),s.lang&&(r.lang=s.lang),s.volume&&(r.volume=s.volume),s.rate&&(r.rate=s.rate),s.pitch&&(r.pitch=s.pitch),r.text=e,Object.keys(c).forEach(function(e){var s=c[e];r[e]=function(n){s&&s(n),'onerror'===e&&t({utterances:d,lastUtterance:r,error:n}),'onend'===e&&l&&a({utterances:d,lastUtterance:r})}}),d.push(r),speechSynthesis.speak(r)})})}},{key:'pending',value:function(){return speechSynthesis.pending}},{key:'paused',value:function(){return speechSynthesis.paused}},{key:'speaking',value:function(){return speechSynthesis.speaking}},{key:'pause',value:function(){speechSynthesis.pause()}},{key:'resume',value:function(){speechSynthesis.resume()}},{key:'cancel',value:function(){speechSynthesis.cancel()}}]),e}();s.default=c},function(e){e.exports={dataFR:{header:{title:'Options d\'accessibilit\xE9 visuelle',profile:'Accessibility Profiles',content:'Ajustements de contenu',display:'Réglages d\'affichage'},buttons:{reset:'R\xE9initialiser tout les r\xE9glages d\'accessibilit\xE9',contrast:'Contraste +',screenReader:'Lecteur de texte',highlight:'Liens en surbrillance',biggerText:'Agrandir le texte',spacingText:'Espacer le texte',dyslexia:'Aide Dyslexie',lineHeight:'Hauteur des lignes',cursor:'Curseur',hideImg:'Masquer les images',saturation:'Saturation'},profiles:{colorBlind:'Daltonien',visuallyImpaired:'Malvoyant',cognitiveLearning:'Cognitif & Apprentissage',seizureEpileptic:'Seizure & Epileptic',adhd:'TDAH',dyslexia:'Dyslexie'},footer:{title:'WebWay-Access ForNet'}},dataEN:{header:{title:'Visual Accessibility Options',profile:'Accessibility Profiles',content:'Content adjustements',display:'Display adjustements'},buttons:{reset:'Reset All Accessibility Settings',contrast:'Contrast +',screenReader:'Screen Reader',highlight:'Highlight Links',biggerText:'Bigger Text',spacingText:'Text Spacing',dyslexia:'Dyslexia Friendly',lineHeight:'Line Height',cursor:'Cursor',hideImg:'Hide Image',saturation:'Saturation'},profiles:{colorBlind:'Color Blind',visuallyImpaired:'Visually-impaired',cognitiveLearning:'Cognitive & Learning',seizureEpileptic:'Seizure & Epileptic',adhd:'ADHD',dyslexia:'Dyslexia'},footer:{title:'WebWay-Access ForNet'}},dataAR:{header:{title:'\u062E\u064A\u0627\u0631\u0627\u062A \u0627\u0644\u0648\u0644\u0648\u062C\u064A\u0629',profile:'\u0625\u0639\u062F\u0627\u062F\u062A \u0645\u0633\u0628\u0642\u0629 \u0644\u0644\u0627\u062E\u062A\u064A\u0627\u0631',content:'\u062A\u0639\u062F\u064A\u0644\u0627\u062A \u0627\u0644\u0645\u062D\u062A\u0648\u0649',display:'\u062A\u0639\u062F\u064A\u0644\u0627\u062A \u0627\u0644\u0639\u0631\u0636'},buttons:{reset:'\u0625\u0639\u0627\u062F\u0629 \u0636\u0628\u0637 \u062C\u0645\u064A\u0639 \u062E\u064A\u0627\u0631\u0627\u062A \u0627\u0644\u0648\u0644\u0648\u062C\u064A\u0629 ',contrast:'\u0627\u0644\u062A\u0628\u0627\u064A\u0646 +',screenReader:'\u0642\u0627\u0631\u0626 \u0627\u0644\u0635\u0641\u062D\u0629',highlight:'\u0625\u0646\u0627\u0631\u0629 \u0627\u0644\u0631\u0648\u0627\u0628\u0637',biggerText:'\u062A\u0643\u0628\u064A\u0631 \u0627\u0644\u0646\u0635',spacingText:'\u062A\u0628\u0627\u0639\u062F \u0627\u0644\u0643\u0644\u0645\u0627\u062A',dyslexia:'\u062A\u064A\u0633\u064A\u0631 \u0639\u0633\u0631 \u0627\u0644\u0642\u0631\u0627\u0621\u0629',lineHeight:'\u0625\u0631\u062A\u0641\u0627\u0639 \u0627\u0644\u0633\u0637\u0631',cursor:'\u0645\u0624\u0634\u0631 \u0627\u0644\u0645\u0627\u0648\u0633',hideImg:'\u0625\u062E\u0641\u0627\u0621 \u0627\u0644\u0635\u0648\u0631',saturation:'\u062A\u0634\u0628\u0639 \u0627\u0644\u0623\u0644\u0648\u0627\u0646'},profiles:{colorBlind:'\u0639\u0645\u0649 \u0627\u0644\u0627\u0644\u0648\u0627\u0646',visuallyImpaired:'\u0636\u0639\u0641 \u0627\u0644\u0628\u0635\u0631',cognitiveLearning:'\u062A\u0639\u0644\u0645 \u0627\u0644\u0645\u0639\u0631\u0641\u064A',seizureEpileptic:'\u0646\u0648\u0628\u0627\u062A \u0627\u0644\u0635\u0631\u0639',adhd:'\u0648\u0636\u0639 \u0645\u0644\u0627\u0626\u0645 \u0644\u0645\u0631\u0636\u0649 ADHD',dyslexia:'\u0639\u0633\u0631 \u0627\u0644\u0642\u0631\u0627\u0621\u0629'},footer:{title:'WebWay-Access ForNet'}}}},function(e){(function(s,a){e.exports=a()})(this,function(){'use strict';/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function e(a,s){var e={};for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&0>s.indexOf(t)&&(e[t]=a[t]);if(null!=a&&'function'==typeof Object.getOwnPropertySymbols)for(var n=0,t=Object.getOwnPropertySymbols(a);n<t.length;n++)0>s.indexOf(t[n])&&Object.prototype.propertyIsEnumerable.call(a,t[n])&&(e[t[n]]=a[t[n]]);return e}var s=Math.PI,a=Math.sin,t=Math.pow,n=function(){return n=Object.assign||function(e){for(var a,s=1,t=arguments.length;s<t;s++)for(var n in a=arguments[s],a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n]);return e},n.apply(this,arguments)},l=function(){function e(e,s){this.ATT_FACTOR=4,this.GRAPH_X=2,this.AMPLITUDE_FACTOR=.6,this.ctrl=e,this.definition=s}return e.prototype.globalAttFn=function(e){return t(this.ATT_FACTOR/(this.ATT_FACTOR+t(e,this.ATT_FACTOR)),this.ATT_FACTOR)},e.prototype.xPos=function(e){return this.ctrl.width*((e+this.GRAPH_X)/(2*this.GRAPH_X))},e.prototype.yPos=function(e){return this.AMPLITUDE_FACTOR*(this.globalAttFn(e)*(this.ctrl.heightMax*this.ctrl.amplitude)*(1/this.definition.attenuation)*a(this.ctrl.opt.frequency*e-this.ctrl.phase))},e.prototype.draw=function(){var e=this.ctrl.ctx;e.moveTo(0,0),e.beginPath();var s=this.definition.color||this.ctrl.color,a=s.replace(/rgb\(/g,'').replace(/\)/g,'');e.strokeStyle='rgba('.concat(a,',').concat(this.definition.opacity,')'),e.lineWidth=this.definition.lineWidth;for(var t=-this.GRAPH_X;t<=this.GRAPH_X;t+=this.ctrl.opt.pixelDepth)e.lineTo(this.xPos(t),this.ctrl.heightMax+this.yPos(t));e.stroke()},e.getDefinition=function(){return[{attenuation:-2,lineWidth:1,opacity:.1},{attenuation:-6,lineWidth:1,opacity:.2},{attenuation:4,lineWidth:1,opacity:.4},{attenuation:2,lineWidth:1,opacity:.6},{attenuation:1,lineWidth:1.5,opacity:1}]},e}(),i=function(){function e(e,s){this.GRAPH_X=25,this.AMPLITUDE_FACTOR=.8,this.SPEED_FACTOR=1,this.DEAD_PX=2,this.ATT_FACTOR=4,this.DESPAWN_FACTOR=.02,this.DEFAULT_NOOFCURVES_RANGES=[2,5],this.DEFAULT_AMPLITUDE_RANGES=[.3,1],this.DEFAULT_OFFSET_RANGES=[-3,3],this.DEFAULT_WIDTH_RANGES=[1,3],this.DEFAULT_SPEED_RANGES=[.5,1],this.DEFAULT_DESPAWN_TIMEOUT_RANGES=[500,2e3],this.ctrl=e,this.definition=s,this.noOfCurves=0,this.spawnAt=0,this.prevMaxY=0,this.phases=[],this.offsets=[],this.speeds=[],this.finalAmplitudes=[],this.widths=[],this.amplitudes=[],this.despawnTimeouts=[],this.verses=[]}return e.prototype.getRandomRange=function(s){return s[0]+Math.random()*(s[1]-s[0])},e.prototype.spawnSingle=function(e){var s,a,t,n,l,i,c,r,o,d;this.phases[e]=0,this.amplitudes[e]=0,this.despawnTimeouts[e]=this.getRandomRange(null!==(a=null===(s=this.ctrl.opt.ranges)||void 0===s?void 0:s.despawnTimeout)&&void 0!==a?a:this.DEFAULT_DESPAWN_TIMEOUT_RANGES),this.offsets[e]=this.getRandomRange(null!==(n=null===(t=this.ctrl.opt.ranges)||void 0===t?void 0:t.offset)&&void 0!==n?n:this.DEFAULT_OFFSET_RANGES),this.speeds[e]=this.getRandomRange(null!==(i=null===(l=this.ctrl.opt.ranges)||void 0===l?void 0:l.speed)&&void 0!==i?i:this.DEFAULT_SPEED_RANGES),this.finalAmplitudes[e]=this.getRandomRange(null!==(r=null===(c=this.ctrl.opt.ranges)||void 0===c?void 0:c.amplitude)&&void 0!==r?r:this.DEFAULT_AMPLITUDE_RANGES),this.widths[e]=this.getRandomRange(null!==(d=null===(o=this.ctrl.opt.ranges)||void 0===o?void 0:o.width)&&void 0!==d?d:this.DEFAULT_WIDTH_RANGES),this.verses[e]=this.getRandomRange([-1,1])},e.prototype.getEmptyArray=function(e){return Array(e)},e.prototype.spawn=function(){var e,s;this.spawnAt=Date.now(),this.noOfCurves=Math.floor(this.getRandomRange(null!==(s=null===(e=this.ctrl.opt.ranges)||void 0===e?void 0:e.noOfCurves)&&void 0!==s?s:this.DEFAULT_NOOFCURVES_RANGES)),this.phases=this.getEmptyArray(this.noOfCurves),this.offsets=this.getEmptyArray(this.noOfCurves),this.speeds=this.getEmptyArray(this.noOfCurves),this.finalAmplitudes=this.getEmptyArray(this.noOfCurves),this.widths=this.getEmptyArray(this.noOfCurves),this.amplitudes=this.getEmptyArray(this.noOfCurves),this.despawnTimeouts=this.getEmptyArray(this.noOfCurves),this.verses=this.getEmptyArray(this.noOfCurves);for(var a=0;a<this.noOfCurves;a++)this.spawnSingle(a)},e.prototype.globalAttFn=function(e){return t(this.ATT_FACTOR/(this.ATT_FACTOR+t(e,2)),this.ATT_FACTOR)},e.prototype.sin=function(e,s){return a(e-s)},e.prototype.yRelativePos=function(e){for(var s,a=0,t=0;t<this.noOfCurves;t++){s=4*(-1+2*(t/(this.noOfCurves-1))),s+=this.offsets[t];var n=1/this.widths[t],l=e*n-s;a+=Math.abs(this.amplitudes[t]*this.sin(this.verses[t]*l,this.phases[t])*this.globalAttFn(l))}return a/this.noOfCurves},e.prototype.yPos=function(e){return this.AMPLITUDE_FACTOR*this.ctrl.heightMax*this.ctrl.amplitude*this.yRelativePos(e)*this.globalAttFn(2*(e/this.GRAPH_X))},e.prototype.xPos=function(e){return this.ctrl.width*((e+this.GRAPH_X)/(2*this.GRAPH_X))},e.prototype.drawSupportLine=function(){var e=this.ctrl.ctx,s=[0,this.ctrl.heightMax,this.ctrl.width,1],a=e.createLinearGradient.apply(e,s);a.addColorStop(0,'transparent'),a.addColorStop(.1,'rgba(255,255,255,.5)'),a.addColorStop(1-.1-.1,'rgba(255,255,255,.5)'),a.addColorStop(1,'transparent'),e.fillStyle=a,e.fillRect.apply(e,s)},e.prototype.draw=function(){var e=Math.max,a=this.ctrl.ctx;if(a.globalAlpha=.7,a.globalCompositeOperation=this.ctrl.opt.globalCompositeOperation,0===this.spawnAt&&this.spawn(),this.definition.supportLine)return this.drawSupportLine();for(var t=0;t<this.noOfCurves;t++)this.spawnAt+this.despawnTimeouts[t]<=Date.now()?this.amplitudes[t]-=this.DESPAWN_FACTOR:this.amplitudes[t]+=this.DESPAWN_FACTOR,this.amplitudes[t]=Math.min(e(this.amplitudes[t],0),this.finalAmplitudes[t]),this.phases[t]=(this.phases[t]+this.ctrl.speed*this.speeds[t]*this.SPEED_FACTOR)%(2*s);for(var n,l=-Infinity,c=0,r=[1,-1];c<r.length;c++){n=r[c],a.beginPath();for(var o=-this.GRAPH_X;o<=this.GRAPH_X;o+=this.ctrl.opt.pixelDepth){var i=this.xPos(o),d=this.yPos(o);a.lineTo(i,this.ctrl.heightMax-n*d),l=e(l,d)}a.closePath(),a.fillStyle='rgba('.concat(this.definition.color,', 1)'),a.strokeStyle='rgba('.concat(this.definition.color,', 1)'),a.fill()}return l<this.DEAD_PX&&this.prevMaxY>l&&(this.spawnAt=0),this.prevMaxY=l,null},e.getDefinition=function(){return[{color:'255,255,255',supportLine:!0},{color:'15, 82, 169'},{color:'173, 57, 76'},{color:'48, 220, 155'}]},e}(),c=function(){function a(s){var a=this,t=s.container,c=e(s,['container']);this.phase=0,this.run=!1,this.curves=[];var r=window.getComputedStyle(t);this.opt=n({container:t,style:'ios',ratio:window.devicePixelRatio||1,speed:.2,amplitude:1,frequency:6,color:'#fff',cover:!1,width:parseInt(r.width.replace('px',''),10),height:parseInt(r.height.replace('px',''),10),autostart:!0,pixelDepth:.02,lerpSpeed:.1,globalCompositeOperation:'lighter'},c),this.speed=+this.opt.speed,this.amplitude=+this.opt.amplitude,this.width=+(this.opt.ratio*this.opt.width),this.height=+(this.opt.ratio*this.opt.height),this.heightMax=+(this.height/2)-6,this.color='rgb('.concat(this.hex2rgb(this.opt.color),')'),this.interpolation={speed:this.speed,amplitude:this.amplitude},this.canvas=document.createElement('canvas');var o=this.canvas.getContext('2d');if(null===o)throw new Error('Unable to create 2D Context');switch(this.ctx=o,this.canvas.width=this.width,this.canvas.height=this.height,!0===this.opt.cover?this.canvas.style.width=this.canvas.style.height='100%':(this.canvas.style.width=''.concat(this.width/this.opt.ratio,'px'),this.canvas.style.height=''.concat(this.height/this.opt.ratio,'px')),this.opt.style){case'ios9':this.curves=(this.opt.curveDefinition||i.getDefinition()).map(function(e){return new i(a,e)});break;case'ios':default:this.curves=(this.opt.curveDefinition||l.getDefinition()).map(function(e){return new l(a,e)});}this.opt.container.appendChild(this.canvas),this.opt.autostart&&this.start()}return a.prototype.hex2rgb=function(e){var s=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;e=e.replace(s,function(e,s,a,t){return s+s+a+a+t+t});var a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return a?''.concat(parseInt(a[1],16).toString(),',').concat(parseInt(a[2],16).toString(),',').concat(parseInt(a[3],16).toString()):null},a.prototype.intLerp=function(e,s,a){return e*(1-a)+s*a},a.prototype.lerp=function(e){var s=this.interpolation[e];return null!==s&&(this[e]=this.intLerp(this[e],s,this.opt.lerpSpeed),0==this[e]-s&&(this.interpolation[e]=null)),this[e]},a.prototype.clear=function(){this.ctx.globalCompositeOperation='destination-out',this.ctx.fillRect(0,0,this.width,this.height),this.ctx.globalCompositeOperation='source-over'},a.prototype.draw=function(){this.curves.forEach(function(e){return e.draw()})},a.prototype.startDrawCycle=function(){this.clear(),this.lerp('amplitude'),this.lerp('speed'),this.draw(),this.phase=(this.phase+s/2*this.speed)%(2*s),window.requestAnimationFrame?this.animationFrameId=window.requestAnimationFrame(this.startDrawCycle.bind(this)):this.timeoutId=setTimeout(this.startDrawCycle.bind(this),20)},a.prototype.start=function(){if(!this.canvas)throw new Error('This instance of SiriWave has been disposed, please create a new instance');this.phase=0,this.run||(this.run=!0,this.startDrawCycle())},a.prototype.stop=function(){this.phase=0,this.run=!1,this.animationFrameId&&window.cancelAnimationFrame(this.animationFrameId),this.timeoutId&&clearTimeout(this.timeoutId)},a.prototype.dispose=function(){this.stop(),this.canvas&&(this.canvas.remove(),this.canvas=null)},a.prototype.set=function(e,s){this.interpolation[e]=s},a.prototype.setSpeed=function(e){this.set('speed',e)},a.prototype.setAmplitude=function(e){this.set('amplitude',e)},a}();return c})},function(e,s,a){e.exports=a(7)},function(e,s){'use strict';Object.defineProperty(s,'__esModule',{value:!0}),s.trim=s.isObject=s.isNil=s.isNan=s.size=s.isString=s.validateLocale=s.splitSentences=void 0;s.splitSentences=function(){var e=0<arguments.length&&arguments[0]!==void 0?arguments[0]:'';return e.replace(/\.+/g,'.|').replace(/\?/g,'?|').replace(/\!/g,'!|').split('|').map(function(e){return n(e)}).filter(Boolean)};var a=/^(?:(en-GB-oed|i-ami|i-bnn|i-default|i-enochian|i-hak|i-klingon|i-lux|i-mingo|i-navajo|i-pwn|i-tao|i-tay|i-tsu|sgn-BE-FR|sgn-BE-NL|sgn-CH-DE)|(art-lojban|cel-gaulish|no-bok|no-nyn|zh-guoyu|zh-hakka|zh-min|zh-min-nan|zh-xiang))$|^((?:[a-z]{2,3}(?:(?:-[a-z]{3}){1,3})?)|[a-z]{4}|[a-z]{5,8})(?:-([a-z]{4}))?(?:-([a-z]{2}|\d{3}))?((?:-(?:[\da-z]{5,8}|\d[\da-z]{3}))*)?((?:-[\da-wy-z](?:-[\da-z]{2,8})+)*)?(-x(?:-[\da-z]{1,8})+)?$|^(x(?:-[\da-z]{1,8})+)$/i;s.validateLocale=function(e){return!('string'!=typeof e)&&a.test(e)};var t=function(e){return'string'==typeof e||e instanceof String};s.isString=t;s.size=function(e){return e&&Array.isArray(e)&&e.length?e.length:0};s.isNan=function(e){return'number'==typeof e&&isNaN(e)};s.isNil=function(e){return null===e||e===void 0};s.isObject=function(e){return'[object Object]'===Object.prototype.toString.call(e)};var n=function(e){return t(e)?e.trim():''};s.trim=n},function(){},function(e,s,a){'use strict';function t(e,s){return c(e)||l(e,s)||n()}function n(){throw new TypeError('Invalid attempt to destructure non-iterable instance')}function l(e,s){var a=[],t=!0,n=!1,l=void 0;try{for(var i,c=e[Symbol.iterator]();!(t=(i=c.next()).done)&&(a.push(i.value),!(s&&a.length===s));t=!0);}catch(e){n=!0,l=e}finally{try{t||null==c['return']||c['return']()}finally{if(n)throw l}}return a}function c(e){if(Array.isArray(e))return e}a.r(s);var i=a(2),r=$('#screenReader'),o=$('#highlightLinks'),d=$('#hideImages'),p=$('#contrastBg'),u=$('#dyslexiaText'),h=$('#cursorGuide'),g=$('#saturationBg'),f=$('#biggerText'),v=$('#spacingText'),m=$('#lineHeightText'),_=$('#toggleAccessPanel, .accessPanel-header__close'),C=$('#accessPanel');$(document).ready(function(){var e='data'+$('html').attr('lang').toUpperCase()||!1,s=i[e],a=s.header.title,t=s.header.profile,n=s.header.content,l=s.header.display,c=s.footer.title,_=s.buttons.contrast,C=s.buttons.screenReader,y=s.buttons.highlight,P=s.buttons.biggerText,x=s.buttons.spacingText,L=s.buttons.dyslexia,b=s.buttons.lineHeight,T=s.buttons.cursor,w=s.buttons.hideImg,A=s.buttons.saturation,S=s.buttons.reset,k=s.profiles.colorBlind,E=s.profiles.visuallyImpaired,H=s.profiles.cognitiveLearning,R=s.profiles.seizureEpileptic,O=s.profiles.adhd,D=s.profiles.dyslexia;$('.accessPanel-header__title').html(a),$('.accessPanel-footer__title').html(c),$('.accessPanel-features__reset__text').html(S),$('#profile_title').html(t),$('#content_title').html(n),$('#display_title').html(l),$('#color_blind ~ .accessPanel-features__checkbox__name').html(k),$('#visually_impaired ~ .accessPanel-features__checkbox__name').html(E),$('#cognitive_learning ~ .accessPanel-features__checkbox__name').html(H),$('#seizure ~ .accessPanel-features__checkbox__name').html(R),$('#adhd ~ .accessPanel-features__checkbox__name').html(O),$('#Dyslexia_toggle ~ .accessPanel-features__checkbox__name').html(D),r.find('.accessPanel-features__item__name').html(C),o.find('.accessPanel-features__item__name').html(y),d.find('.accessPanel-features__item__name').html(w),p.find('.accessPanel-features__item__name').html(_),u.find('.accessPanel-features__item__name').html(L),h.find('.accessPanel-features__item__name').html(T),g.find('.accessPanel-features__item__name').html(A),f.find('.accessPanel-features__item__name').html(P),v.find('.accessPanel-features__item__name').html(x),m.find('.accessPanel-features__item__name').html(b)});var y=a(0),P=a(1),x=a.n(P),L=a(3),b=a.n(L);$(function(){function e(){var e=new x.a,a=function(){var e=speechSynthesis.getVoices();if(0<e.length){var s=e[0].lang.slice(0,2),a=e.some(function(e){return e.lang.slice(0,2)===u});return a}return!1};window.speechSynthesis.onvoiceschanged=a,a(),console,e.init({volume:1,lang:u,rate:i,pitch:1,listeners:{onvoiceschanged:function(e){console.log('Voices changed',e)}}}).then(function(t){return console.log('Speech is ready',t),a()?void(g(t.voices),s(e)):(console.error('The current language is not supported by your browser !'),void f())}).catch(function(s){console.error('An error occured while initializing : ',s)});e.hasBrowserSupport()?'Hurray, your browser supports speech synthesis':'Your browser does NOT support speech synthesis. Try using Chrome of Safari instead !'}function s(e){var s,t=document.getElementById('play'),n=document.getElementById('stopButton'),l=document.getElementById('lineWave'),r=document.getElementById('languages'),o=document.getElementById('speedButton');t.addEventListener('click',function(){if(console.log(t.classList.contains('play')),t.classList.contains('play')){var n=r.value,i=r.options[r.selectedIndex].dataset.name;n&&e.setLanguage(r.value),i&&e.setVoice(i),s=new b.a({container:l,ratio:2,frequency:5,amplitude:0,autostart:!0,speed:c,color:'#3368f2'}),e.speak({text:a,queue:!1,listeners:{onstart:function(){console.log('Start utterance'),setTimeout(function(){s.setAmplitude(1)},300)},onend:function(){console.log('End utterance'),s.setAmplitude(0)},onresume:function(){console.log('Resume utterance')}}}).then(function(e){console.log('Success !',e),s.dispose(),t.classList.contains('pause')?(t.classList.remove('pause'),t.classList.add('play'),t.innerHTML=d):t.classList.contains('resume')&&(t.classList.remove('resume'),t.classList.add('play'))}).catch(function(s){console.error('An error occurred :',s)}),t.classList.remove('play'),t.classList.add('pause'),t.innerHTML=p}else t.classList.contains('pause')?(e.pause(),t.classList.remove('pause'),t.classList.add('resume'),t.innerHTML=d,s.setAmplitude(0)):(e.resume(),t.classList.remove('resume'),t.classList.add('pause'),t.innerHTML=p,s.setAmplitude(1))}),n.addEventListener('click',function(){e.cancel(),s.dispose(),t.classList.contains('pause')?(t.classList.remove('pause'),t.classList.add('play'),t.innerHTML=d):t.classList.contains('resume')&&(t.classList.remove('resume'),t.classList.add('play'))}),o.addEventListener('click',function(){o.classList.contains('level-1')?(i=1.5,o.classList.remove('level-1'),o.classList.add('level-2'),o.innerText='1.5\xD7',c=.3):o.classList.contains('level-2')?(i=2,o.classList.remove('level-2'),o.innerText='2.0\xD7',c=.5):(i=1,o.classList.add('level-1'),o.innerText='1.0\xD7',c=.2),e.setRate(i)})}var a,n=!1,l=!1,i=1,c=.2,r=document.getElementById('navBar'),o=document.getElementById('screenReader'),d='<svg width="30px" height="30px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM9.64109 7.19733C9.14132 6.89192 8.5 7.2516 8.5 7.83729V16.1627C8.5 16.7484 9.14132 17.1081 9.64109 16.8027L16.4528 12.64C16.9313 12.3475 16.9313 11.6525 16.4528 11.36L9.64109 7.19733Z" fill="#3368f2"/></svg>',p='<svg width="30px" height="30px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM8 8C8 7.44772 8.44772 7 9 7H10C10.5523 7 11 7.44772 11 8V16C11 16.5523 10.5523 17 10 17H9C8.44772 17 8 16.5523 8 16V8ZM13 8C13 7.44772 13.4477 7 14 7H15C15.5523 7 16 7.44772 16 8V16C16 16.5523 15.5523 17 15 17H14C13.4477 17 13 16.5523 13 16V8Z" fill="#3368f2"/></svg>',u=document.documentElement.lang,h=function(){var e;if(window.getSelection&&(e=window.getSelection()).modify){if(e=window.getSelection(),!e.isCollapsed){var s=document.querySelectorAll('.voice-control'),t=document.getElementById('infoVoiceControl'),n=document.createRange();n.setStart(e.anchorNode,e.anchorOffset),n.setEnd(e.focusNode,e.focusOffset);var l=n.collapsed;n.detach();var i=e.focusNode,c=e.focusOffset;e.collapse(e.anchorNode,e.anchorOffset);var r=[];r=l?['backward','forward']:['forward','backward'],e.modify('move',r[0],'character'),e.modify('move',r[1],'word'),e.extend(i,c),e.modify('extend',r[1],'character'),e.modify('extend',r[0],'word'),a=e.toString(),s.forEach(function(e){e.disabled=!1}),t.style.display='none',t.classList.add('hideControl')}}else if((e=document.selection)&&'Control'!=e.type){var o=e.createRange();if(o.text){for(o.expand('word');/\s$/.test(o.text);)o.moveEnd('character',-1);o.select()}}};o.onclick=function(){n=!n;var e=new x.a;if(n){document.addEventListener('mouseup',h);var s={fr:'S\xE9lectionnez un texte et appuyez sur le bouton de lecture',en:'Select any text and hit the play button',ar:'\u062D\u062F\u062F \u0623\u064A \u0646\u0635 \u0648\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0632\u0631 \u0627\u0644\u062A\u0634\u063A\u064A\u0644'},a=s[u]||s.en;e.setLanguage(u),e.setRate(1.25),e.speak({text:a}).then(function(){console.log('Success !')}).catch(function(s){console.error('An error occurred :',s)})}else document.removeEventListener('mouseup',h),e.speak({text:'Screen Reader disabled'}).then(function(){console.log('Success !')}).catch(function(s){console.error('An error occurred :',s)})},$('.accessPanel-features__reset').on('click',function(){console.log('BUtton has been clicked'+n),n&&($('body').removeClass('reader-enable'),document.removeEventListener('mouseup',h),n=!n)});var g=function(e){var s=window.document.createElement('div');s.classList.add('voices-list-wrraper'),s.classList.add('accessPanel-features__wrapper');var a={fr:'S\xE9lectionnez un texte et appuyez sur le bouton de lecture',en:'Select any text and hit the play button',ar:'\u062D\u062F\u062F \u0623\u064A \u0646\u0635 \u0648\u0627\u0636\u063A\u0637 \u0639\u0644\u0649 \u0632\u0631 \u0627\u0644\u062A\u0634\u063A\u064A\u0644'},n=a[u]||a.en,i='<div class="accessPanel-features__title" id="infoVoiceControl">   <span class="accessPanel-features__title__s">'+n+'</span></div><button class="accessPanel-features__btn voice-control play" id="play" disabled="">'+d+'</button><button class="accessPanel-features__btn voice-control" id="stopButton" disabled="">   <svg fill="#000000" height="27px" width="27px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 297 297" xml:space="preserve"><path d="M148.5,0C66.486,0,0,66.486,0,148.5S66.486,297,148.5,297S297,230.514,297,148.5S230.514,0,148.5,0z M213.292,190.121  c0,12.912-10.467,23.379-23.378,23.379H106.67c-12.911,0-23.378-10.467-23.378-23.379v-83.242c0-12.912,10.467-23.379,23.378-23.379  h83.244c12.911,0,23.378,10.467,23.378,23.379V190.121z"/></svg></button><button class="accessPanel-features__btn voice-control level-1" id="speedButton" disabled="">   1.0\xD7</button><button class="accessPanel-features__btn" id="collapseVoice">   <svg width="10" height="10" viewBox="0 0 10 10" fill="#000000" xmlns="http://www.w3.org/2000/svg" style="color: #000000;" class="sc-fbkiRW cjjfHa"><path d="M0.901804 4.49399H3.68236C4.19339 4.49399 4.49399 4.19339 4.49399 3.68236V0.906814C4.49399 0.646293 4.2986 0.440882 4.02806 0.440882C3.76253 0.440882 3.57214 0.641283 3.57214 0.906814V1.32766L3.67736 3.07615L2.35471 1.68838L0.816633 0.135271C0.731463 0.0450902 0.616233 0 0.495992 0C0.210421 0 0 0.190381 0 0.470942C0 0.601202 0.0501002 0.726453 0.140281 0.816633L1.68337 2.35972L3.07615 3.67735L1.32265 3.57715H0.901804C0.636273 3.57715 0.430862 3.76253 0.430862 4.03307C0.430862 4.2986 0.631263 4.49399 0.901804 4.49399ZM5.70641 9.57916C5.97194 9.57916 6.16233 9.38377 6.16233 9.11323V8.63727L6.05711 6.89379L7.37976 8.28156L8.95291 9.86473C9.03808 9.95491 9.15331 10 9.27355 10C9.55411 10 9.76954 9.80962 9.76954 9.52906C9.76954 9.3988 9.71944 9.27355 9.62926 9.18337L8.0511 7.60521L6.66333 6.28758L8.41182 6.38778H8.88778C9.15331 6.38778 9.35872 6.2024 9.35872 5.93687C9.35872 5.66633 9.15832 5.47595 8.88778 5.47595H6.0521C5.54108 5.47595 5.24549 5.77154 5.24549 6.28257V9.11323C5.24549 9.37876 5.44088 9.57916 5.70641 9.57916Z"></path></svg></button><div id="lineWave"></div><div class="accessPanel-features__select"> <div class="accessPanel-features__title">   <span class="accessPanel-features__title__s">Available Voices</span> </div> <select id="languages"><option value="">autodetect language</option></div>',c=new Map,r=!0,o=!1,p=void 0;try{for(var h,g,f=e[Symbol.iterator]();!(r=(h=f.next()).done);r=!0)g=h.value,c.has(g.lang.slice(0,-3))||c.set(g.lang.slice(0,-3),g)}catch(e){o=!0,p=e}finally{try{r||null==f.return||f.return()}finally{if(o)throw p}}var v=!0,m=!1,_=void 0;try{for(var C,y=c[Symbol.iterator]();!(v=(C=y.next()).done);v=!0){var P=t(C.value,2),x=P[0],L=P[1];i+='<option value="'.concat(L.lang,'" data-name="').concat(L.name,'">').concat(L.name,' (').concat(L.lang,')</option>')}}catch(e){m=!0,_=e}finally{try{v||null==y.return||y.return()}finally{if(m)throw _}}s.innerHTML=i,document.body.appendChild(s);var b=document.getElementById('collapseVoice');b.addEventListener('click',function(){l=!l,b.parentNode.classList.toggle('active'),b.innerHTML=l?'<svg width="10" height="10" viewBox="0 0 10 10" fill="#000000" xmlns="http://www.w3.org/2000/svg" style="color: #000000;"><path d="M0.487421 4.24004C0.759958 4.24004 0.964361 4.0304 0.964361 3.75262V3.31237L0.854298 1.48323L2.23795 2.93501L3.84696 4.55975C3.93606 4.65409 4.05136 4.69602 4.18239 4.69602C4.47589 4.69602 4.70126 4.5021 4.70126 4.2086C4.70126 4.06709 4.64885 3.9413 4.55451 3.84696L2.94025 2.2327L1.48323 0.854298L3.31761 0.959119H3.75786C4.03564 0.959119 4.25052 0.765199 4.25052 0.48218C4.25052 0.199161 4.03564 0 3.75786 0H0.843816C0.309224 0 0 0.309224 0 0.843816V3.75262C0 4.02516 0.204403 4.24004 0.487421 4.24004ZM6.23166 10H9.1457C9.68029 10 9.98952 9.69077 9.98952 9.15618V6.24738C9.98952 5.97484 9.78512 5.75996 9.5021 5.75996C9.22956 5.75996 9.0304 5.9696 9.0304 6.24738V6.68763L9.13522 8.51677L7.75157 7.06499L6.14256 5.44025C6.05346 5.34591 5.93815 5.30398 5.80713 5.30398C5.51363 5.30398 5.2935 5.4979 5.2935 5.7914C5.2935 5.93291 5.34067 6.0587 5.43501 6.15304L7.05451 7.7673L8.50629 9.1457L6.67191 9.04088H6.23166C5.95388 9.04088 5.74423 9.2348 5.73899 9.51782C5.73899 9.80084 5.95388 10 6.23166 10Z"></path></svg>':'<svg width="10" height="10" viewBox="0 0 10 10" fill="#000000" xmlns="http://www.w3.org/2000/svg" style="color: #000000;"><path d="M0.901804 4.49399H3.68236C4.19339 4.49399 4.49399 4.19339 4.49399 3.68236V0.906814C4.49399 0.646293 4.2986 0.440882 4.02806 0.440882C3.76253 0.440882 3.57214 0.641283 3.57214 0.906814V1.32766L3.67736 3.07615L2.35471 1.68838L0.816633 0.135271C0.731463 0.0450902 0.616233 0 0.495992 0C0.210421 0 0 0.190381 0 0.470942C0 0.601202 0.0501002 0.726453 0.140281 0.816633L1.68337 2.35972L3.07615 3.67735L1.32265 3.57715H0.901804C0.636273 3.57715 0.430862 3.76253 0.430862 4.03307C0.430862 4.2986 0.631263 4.49399 0.901804 4.49399ZM5.70641 9.57916C5.97194 9.57916 6.16233 9.38377 6.16233 9.11323V8.63727L6.05711 6.89379L7.37976 8.28156L8.95291 9.86473C9.03808 9.95491 9.15331 10 9.27355 10C9.55411 10 9.76954 9.80962 9.76954 9.52906C9.76954 9.3988 9.71944 9.27355 9.62926 9.18337L8.0511 7.60521L6.66333 6.28758L8.41182 6.38778H8.88778C9.15331 6.38778 9.35872 6.2024 9.35872 5.93687C9.35872 5.66633 9.15832 5.47595 8.88778 5.47595H6.0521C5.54108 5.47595 5.24549 5.77154 5.24549 6.28257V9.11323C5.24549 9.37876 5.44088 9.57916 5.70641 9.57916Z"></path></svg>'})},f=function(){var e=window.document.createElement('div');e.classList.add('voices-list-wrraper'),e.classList.add('accessPanel-features__wrapper');var s={fr:'La langue actuelle n\'est pas prise en charge par votre navigateur !',en:'The current language is not supported by your browser !',ar:'\u0627\u0644\u0644\u063A\u0629 \u0627\u0644\u062D\u0627\u0644\u064A\u0629 \u063A\u064A\u0631 \u0645\u062F\u0639\u0648\u0645\u0629 \u0645\u0646 \u0642\u0628\u0644 \u0627\u0644\u0645\u062A\u0635\u0641\u062D \u0627\u0644\u062E\u0627\u0635 \u0628\u0643 !'},a=s[u]||s.en;e.innerHTML='<div class="accessPanel-features__title" id="infoVoiceControl">   <span class="accessPanel-features__title__s">'+a+'</span></div>',document.body.appendChild(e);document.getElementById('collapseVoice')};e()});a(6);(function(s){s(document).ready(function(){function e(e){var a=e.parent();a.toggleClass('accessPanel-features__item_active'),a.hasClass('accessPanel-features__item_active')?s('<span class="accessPanel-features__item__enabled"><span style="width: 9px; height: 6px; display: flex;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 8" width="100%" height="100%"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="m1.5 4.5 2 2 5-5"></path></svg></span></span>').appendTo(e):e.find('.accessPanel-features__item__enabled').remove()}function a(e){try{for(var s,a=document.querySelectorAll('body *:not([class*="accessPanel"])'),t=0;t<a.length;t++)s=a[t],s.textContent.trim()&&s.classList.add(e)}catch(e){console.error(e)}}function t(e,a,t){var n=s(e).attr('class'),l=n.split(' ');console.log(l,a.length,t,a[t]),s(e).addClass(a[t]),s(e).removeClass(a[t-1])}function n(e,s,a){a.parent().hasClass('accessPanel-features__item_active')||a.hasClass('accessPanel-features__item__r')?(y.set(e,s,{expires:7}),console.log('add Cookie')):(y.remove(e),console.log('Remove Cookie !'))}function l(){try{S=0,k=0,E=0,H=0,R=0,O=0,D=0,y.remove('accessPanelProfile'),s('img').each(function(){s(this).removeClass('hide-image')}),n('accessPanelHideimg',null,c),s('a').each(function(){s(this).removeClass('highlight')}),n('accessPanelHighlightL',null,i),s('.accessPanel-features__item_active > button').each(function(){var a=s(this);e(a),a.find('.accessPanel-features__step').removeClass('active'),a.hasClass(M[0])&&(s('html').removeClass('accessPanel-1-contrast'),n('accessPanelContrast',null,a)),a.hasClass(M[1])&&(s('html').removeClass('accessPanel-1-contrast-dark'),n('accessPanelContrast',null,a)),(a.hasClass(F[0])||a.hasClass(F[1])||a.hasClass(F[2])||a.hasClass(F[3]))&&(s('html').css('font-size',''),n('accessPanelTextB',null,a)),a.hasClass(I[0])&&(s('html').removeClass('accessPanel-spacing-1'),n('accessPanelSpacingText',null,a)),a.hasClass(I[1])&&(s('html').removeClass('accessPanel-spacing-2'),n('accessPanelSpacingText',null,a)),a.hasClass(I[2])&&(s('html').removeClass('accessPanel-spacing-3'),n('accessPanelSpacingText',null,a)),a.hasClass(V[0])&&(s('html').removeClass('accessPanel-LineH-1'),n('accessPanelLineH',null,a)),a.hasClass(V[1])&&(s('html').removeClass('accessPanel-LineH-2'),n('accessPanelLineH',null,a)),a.hasClass(V[2])&&(s('html').removeClass('accessPanel-LineH-3'),n('accessPanelLineH',null,a)),a.hasClass(z[0])&&(s('body').removeClass('accessPanel-1-cursor'),n('accessPanelCursor',null,a)),a.hasClass(z[1])&&(s('#cursorMaskTop').remove(),s('#cursorMaskBottom').remove(),n('accessPanelCursor',null,a)),a.hasClass(z[2])&&(s('#cursorLine').remove(),n('accessPanelCursor',null,a)),(a.hasClass(B[0])||a.hasClass(B[1])||a.hasClass(B[2]))&&(s('html').css('filter',''),n('accessPanelSaturate',null,a)),a.hasClass(N[0])&&(s('.element_contain_text').removeClass('element_contain_text'),n('accessPanelDyslexia',null,a)),a.hasClass(N[1])&&(s('.element_contain_text_arial').removeClass('element_contain_text_arial'),n('accessPanelDyslexia',null,a)),a.attr('class','accessPanel-features__item__i')})}catch(e){console.error(e)}}var i=o,c=d,P=p,x=u,L=h,b=g,T=f,w=v,A=m,S=0,k=0,E=0,H=0,R=0,O=0,D=0,M=['active-1','active-2'],B=['saturationLevelOne','saturationLevelTwo','saturationLevelThree'],F=['textLevelOne','textLevelTwo','textLevelThree','textLevelFor'],N=['dysLevelOne','dysLevelTwo'],I=['spacingLevelOne','spacingLevelTwo','spacingLevelThree'],V=['lineHLevelOne','lineHLevelTwo','LineHLevelThree'],z=['largeCursor','guideMaskCursor','guideLineCursor'],U={profile:y.get('accessPanelProfile')||0,hightLightLink:y.get('accessPanelHighlightL')||0,hideImages:y.get('accessPanelHideimg')||0,dyslexia:y.get('accessPanelDyslexia')||0,spaceTxt:y.get('accessPanelSpacingText')||0,lineH:y.get('accessPanelLineH')||0,contrast:y.get('accessPanelContrast')||0,textBigger:y.get('accessPanelTextB')||0,cursor:y.get('accessPanelCursor')||0,saturate:y.get('accessPanelSaturate')||0};console.log(U),0!==U.profile&&setTimeout(function(){s('#'+U.profile).prop('checked',!0).change()},300),'active'==U.hightLightLink&&(e(i),s('a').each(function(){s(this).toggleClass('highlight')})),'active'==U.hideImages&&(e(c),s('img').each(function(){s(this).toggleClass('hide-image')})),'InvertActive'==U.contrast?(e(P),P.addClass(M[0]),P.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').addClass('accessPanel-1-contrast'),S++):'DarkModActive'==U.contrast&&(e(P),P.addClass(M[1]),P.find('.accessPanel-features__step').addClass('active'),s('html').addClass('accessPanel-1-contrast-dark'),S=2),'LevelOne'==U.saturate?(e(b),b.addClass(B[0]),b.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').css('filter','saturate(0.5)'),k++):'LevelTwo'==U.saturate?(e(b),b.addClass(B[1]),b.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),s('html').css('filter','saturate(3)'),k=2):'LevelThree'==U.saturate&&(e(b),b.addClass(B[2]),b.find('.accessPanel-features__step').addClass('active'),s('html').css('filter','saturate(0)'),k=3),0!==U.textBigger&&(e(T),s('html').css('font-size',U.textBigger+'px'),'17'==U.textBigger?(T.addClass(F[0]),T.find('.accessPanel-features__step:nth-child(1)').addClass('active'),E++):'18'==U.textBigger?(T.addClass(F[1]),T.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),E=2):'20'==U.textBigger?(T.addClass(F[2]),T.find('.accessPanel-features__step:nth-child(-n + 3)').addClass('active'),E=3):'22'==U.textBigger&&(T.addClass(F[3]),T.find('.accessPanel-features__step').addClass('active'),E=4)),0!==U.dyslexia&&(e(x),a('element_contain_text'),'dyslexia'==U.dyslexia?(x.addClass(N[0]),x.find('.accessPanel-features__step:nth-child(1)').addClass('active'),H++):(x.addClass(N[1]),x.find('.accessPanel-features__step').addClass('active'),H=2,s('.element_contain_text').removeClass('element_contain_text').addClass('element_contain_text_arial'))),0!==U.spaceTxt&&(e(w),'levelOne'==U.spaceTxt?(w.addClass(I[0]),w.find('.accessPanel-features__step:nth-child(1)').addClass('active'),R++,s('html').addClass('accessPanel-spacing-1')):'levelTwo'==U.spaceTxt?(w.addClass(I[1]),w.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),R=2,s('html').addClass('accessPanel-spacing-2')):(w.addClass(I[2]),w.find('.accessPanel-features__step').addClass('active'),R=3,s('html').addClass('accessPanel-spacing-3'))),0!==U.lineH&&(e(A),'levelOne'==U.lineH?(A.addClass(V[0]),A.find('.accessPanel-features__step:nth-child(1)').addClass('active'),O++,s('html').addClass('accessPanel-LineH-1')):'levelTwo'==U.lineH?(A.addClass(V[1]),A.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),O=2,s('html').addClass('accessPanel-LineH-2')):(A.addClass(V[2]),A.find('.accessPanel-features__step').addClass('active'),O=3,s('html').addClass('accessPanel-LineH-3'))),0!==U.cursor&&(e(L),'largeCursor'==U.cursor?(L.addClass(z[0]),L.find('.accessPanel-features__step:nth-child(1)').addClass('active'),D++,s('body').addClass('accessPanel-1-cursor')):'guideMask'==U.cursor?(L.addClass(z[1]),L.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),s('<div id="cursorMaskTop" style="top:480px"></div><div id="cursorMaskBottom" style="top:600px"></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientY;s('#cursorMaskTop').css('top',e-60),s('#cursorMaskBottom').css('top',e+60)}),D=2):(L.addClass(z[2]),L.find('.accessPanel-features__step').addClass('active'),D=3,s('<div id="cursorLine" ></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientX,t=a.clientY-15;s('#cursorLine').css({left:e,top:t})})));var G=s('.accessPanel-features__item__r');G.each(function(){var t=s(this).attr('id');s(this).change(function(){G.not(s(this)).prop('checked',!1),l();var i=s(this).is(':checked');console.log('StateCHeckbox: '+i+' - id: '+t),i&&(n('accessPanelProfile',t,s(this)),'color_blind'==t&&(e(b),b.addClass(B[1]),b.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),s('html').css('filter','saturate(3)'),k=2),'visually_impaired'==t&&(e(T),e(x),e(L),e(b),s('html').css('font-size','17px'),T.addClass(F[0]),T.find('.accessPanel-features__step:nth-child(1)').addClass('active'),E=1,a('element_contain_text'),x.addClass(N[1]),x.find('.accessPanel-features__step').addClass('active'),H=2,s('.element_contain_text').removeClass('element_contain_text').addClass('element_contain_text_arial'),L.addClass(z[0]),L.find('.accessPanel-features__step:nth-child(1)').addClass('active'),D=1,s('body').addClass('accessPanel-1-cursor'),b.addClass(B[1]),b.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),s('html').css('filter','saturate(3)'),k=2),'cognitive_learning'==t&&(e(T),e(L),s('html').css('font-size','17px'),T.addClass(F[0]),T.find('.accessPanel-features__step:nth-child(1)').addClass('active'),E=1,L.addClass(z[2]),L.find('.accessPanel-features__step').addClass('active'),D=3,s('<div id="cursorLine" ></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientX,t=a.clientY-15;s('#cursorLine').css({left:e,top:t})})),'seizure'==t&&(e(b),b.addClass(B[0]),b.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').css('filter','saturate(0.5)'),k=1),'adhd'==t&&(e(b),e(L),b.addClass(B[0]),b.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').css('filter','saturate(0.5)'),k=1,L.addClass(z[1]),L.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),s('<div id="cursorMaskTop" style="top:480px"></div><div id="cursorMaskBottom" style="top:600px"></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientY;s('#cursorMaskTop').css('top',e-60),s('#cursorMaskBottom').css('top',e+60)}),D=2),'Dyslexia_toggle'==t&&(e(x),a('element_contain_text'),x.addClass(N[0]),x.find('.accessPanel-features__step:nth-child(1)').addClass('active'),H=1))})}),_.on('click',function(){C.hasClass('visible')?C.toggleClass('visible'):C.toggleClass('visible')}),s(document).click(function(e){var a=s(e.target);!a.closest('#accessPanel, #toggleAccessPanel').length&&s('#accessPanel').hasClass('visible')&&C.toggleClass('visible')}),r.on('click',function(){e(s(this)),s('body').toggleClass('reader-enable')}),i.on('click',function(){var a=s(this);e(s(this)),s('a').each(function(){s(this).toggleClass('highlight')}),n('accessPanelHighlightL','active',a)}),c.on('click',function(){var a=s(this);e(a),s('img').each(function(){s(this).toggleClass('hide-image')}),n('accessPanelHideimg','active',a)}),x.on('click',function(){var l=s(this);t(l,N,H),H===N.length?(H=0,e(l),l.find('.accessPanel-features__step').removeClass('active'),s('.element_contain_text_arial').removeClass('element_contain_text_arial'),n('accessPanelDyslexia',null,l)):H++,l.hasClass(N[0])&&(e(l),l.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('element_contain_text'),n('accessPanelDyslexia','dyslexia',l)),l.hasClass(N[1])&&(l.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('.element_contain_text').removeClass('element_contain_text').addClass('element_contain_text_arial'),n('accessPanelDyslexia','arial',l))}),P.on('click',function(){var a=s(this);t(a,M,S),S===M.length?(S=0,e(a),a.find('.accessPanel-features__step').removeClass('active'),s('html').removeClass('accessPanel-1-contrast-dark'),n('accessPanelContrast','InvertActive',a)):S++,a.hasClass(M[0])&&(e(a),a.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').addClass('accessPanel-1-contrast'),n('accessPanelContrast','InvertActive',a)),a.hasClass(M[1])&&(a.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('html').removeClass('accessPanel-1-contrast').addClass('accessPanel-1-contrast-dark'),n('accessPanelContrast','DarkModActive',a))}),L.on('click',function(){var a=s(this);t(a,z,D),D===z.length?(D=0,e(a),a.find('.accessPanel-features__step').removeClass('active'),s('#cursorLine').remove(),n('accessPanelCursor',null,a)):D++,a.hasClass(z[0])&&(e(a),a.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('body').addClass('accessPanel-1-cursor'),n('accessPanelCursor','largeCursor',a)),a.hasClass(z[1])&&(a.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('body').removeClass('accessPanel-1-cursor'),s('<div id="cursorMaskTop" style="top:480px"></div><div id="cursorMaskBottom" style="top:600px"></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientY;s('#cursorMaskTop').css('top',e-60),s('#cursorMaskBottom').css('top',e+60)}),n('accessPanelCursor','guideMask',a)),a.hasClass(z[2])&&(a.find('.accessPanel-features__step:nth-child(3)').addClass('active'),s('#cursorMaskTop').remove(),s('#cursorMaskBottom').remove(),s('<div id="cursorLine" ></div>').appendTo('body'),s(document).on('mousemove',function(a){var e=a.clientX,t=a.clientY-15;s('#cursorLine').css({left:e,top:t})}),n('accessPanelCursor','guideLine',a))}),b.on('click',function(a){a.preventDefault();var l=s(this);t(l,B,k),k===B.length?(k=0,e(l),l.find('.accessPanel-features__step').removeClass('active'),s('html').css('filter',''),n('accessPanelSaturate',null,l)):k++,l.hasClass(B[0])&&(e(l),l.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').css('filter','saturate(0.5)'),n('accessPanelSaturate','LevelOne',l)),l.hasClass(B[1])&&(l.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('html').css('filter','saturate(3)'),n('accessPanelSaturate','LevelTwo',l)),l.hasClass(B[2])&&(l.find('.accessPanel-features__step:nth-child(3)').addClass('active'),s('html').css('filter','saturate(0)'),n('accessPanelSaturate','LevelThree',l))}),T.on('click',function(a){a.preventDefault();var l=s(this);t(l,F,E),E===F.length?(E=0,e(l),l.find('.accessPanel-features__step').removeClass('active'),s('html').css('font-size',''),n('accessPanelTextB',null,l)):E++,l.hasClass(F[0])&&(e(l),l.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').css('font-size','17px'),n('accessPanelTextB','17',l)),l.hasClass(F[1])&&(l.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('html').css('font-size','18px'),n('accessPanelTextB','18',l)),l.hasClass(F[2])&&(l.find('.accessPanel-features__step:nth-child(3)').addClass('active'),s('html').css('font-size','20px'),n('accessPanelTextB','20',l)),l.hasClass(F[3])&&(l.find('.accessPanel-features__step:nth-child(4)').addClass('active'),s('html').css('font-size','22px'),n('accessPanelTextB','22',l))}),w.on('click',function(a){a.preventDefault();var l=s(this);t(l,I,R),R===I.length?(R=0,e(l),l.find('.accessPanel-features__step').removeClass('active'),s('html').removeClass('accessPanel-spacing-3'),n('accessPanelSpacingText',null,l)):R++,l.hasClass(I[0])&&(e(l),l.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').addClass('accessPanel-spacing-1'),n('accessPanelSpacingText','levelOne',l)),l.hasClass(I[1])&&(l.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('html').removeClass('accessPanel-spacing-1').addClass('accessPanel-spacing-2'),n('accessPanelSpacingText','levelTwo',l)),l.hasClass(I[2])&&(l.find('.accessPanel-features__step:nth-child(3)').addClass('active'),s('html').removeClass('accessPanel-spacing-2').addClass('accessPanel-spacing-3'),n('accessPanelSpacingText','levelThree',l))}),A.on('click',function(a){a.preventDefault();var l=s(this);t(l,V,O),O===V.length?(O=0,e(l),l.find('.accessPanel-features__step').removeClass('active'),n('accessPanelLineH',null,l),s('html').removeClass('accessPanel-LineH-3')):O++,l.hasClass(V[0])&&(e(l),l.find('.accessPanel-features__step:nth-child(1)').addClass('active'),s('html').addClass('accessPanel-LineH-1'),n('accessPanelLineH','levelOne',l)),l.hasClass(V[1])&&(l.find('.accessPanel-features__step:nth-child(2)').addClass('active'),s('html').removeClass('accessPanel-LineH-1').addClass('accessPanel-LineH-2'),n('accessPanelLineH','levelTwo',l)),l.hasClass(V[2])&&(l.find('.accessPanel-features__step:nth-child(3)').addClass('active'),s('html').removeClass('accessPanel-LineH-2').addClass('accessPanel-LineH-3'),n('accessPanelLineH','levelThree',l))}),s('.accessPanel-features__reset').on('click',function(){G.prop('checked',!1),l()}),s('.accessPanel-features__item__i').on('click',function(){G.prop('checked',!1)})})})(jQuery)}]);})(jQuery);