# Module Import PDF ZIP - Documentation

## Description
Ce module permet d'importer automatiquement des fichiers PDF depuis un fichier ZIP vers des nœuds de réglementation Drupal. Les PDFs sont associés aux nœuds en fonction de la correspondance entre le nom du fichier PDF et le titre du nœud.

## Fonctionnalités principales

### ✅ Import automatique
- Upload d'un fichier ZIP contenant des PDFs
- Extraction automatique des PDFs
- Association avec les nœuds par correspondance de titre
- Attachment au champ `field_lien_telechargement`

### ✅ Support multilingue avancé
- **Détection automatique de la langue** (arabe/français)
- **Normalisation des caractères arabes** (hamza, ya, ta marbouta)
- **Recherche approximative** avec calcul de similarité
- **Support UTF-8** complet

### ✅ Recherche intelligente
- Recherche exacte par titre
- Recherche avec titre normalisé
- Recherche approximative (seuil de 80% de similarité)
- Génération de variantes de titre automatique
- Suggestions de nœuds similaires en cas d'échec

### ✅ Options avancées
- **Mode simulation** (dry run) pour tester sans importer
- **Écrasement des fichiers existants**
- **Langue forcée** (override de la détection automatique)
- **Traitement par batch** pour éviter les timeouts

### ✅ Interface utilisateur riche
- Affichage détaillé des résultats
- Liste des fichiers importés avec succès
- Détails des erreurs et suggestions
- Conseils d'utilisation contextuelle

## Installation et configuration

### Prérequis
- Extension PHP `ZipArchive`
- Champ `field_lien_telechargement` sur le type de contenu `reglementation`
- Permissions d'écriture sur `sites/default/files/`

### Activation
```bash
drush en import_reglementation
```

### Test de fonctionnement
```bash
cd /var/www/html/mtl
php web/modules/custom/import_reglementation/test_pdf_zip.php
```

## Utilisation

### Accès au formulaire
Naviguer vers `/admin/content/import-pdf-zip`

### Préparation des fichiers
1. **Noms des fichiers PDF** : Doivent correspondre exactement aux titres des nœuds
2. **Encodage** : UTF-8 recommandé pour les caractères arabes
3. **Structure ZIP** : PDFs à la racine ou dans des sous-dossiers

### Exemples de correspondances

#### Titres exacts
```
PDF: "قانون المالية.pdf" 
Nœud: "قانون المالية" (ar)
✅ Correspondance directe
```

#### Normalisation automatique
```
PDF: "قانون المالیة.pdf" (ya final)
Nœud: "قانون المالية" (ya normal) 
✅ Correspondance après normalisation
```

#### Recherche approximative
```
PDF: "Code de la route 2024.pdf"
Nœud: "Code de la route"
✅ Correspondance approximative (87% similarité)
```

## Architecture technique

### Services Drupal
- `import_reglementation.pdf_zip_processor` : Logique de traitement
- Injection de dépendance : `entity_type.manager`, `file_system`, `logger.factory`

### Méthodes clés

#### `PdfZipProcessor::processSinglePdfFromZip()`
Traite un fichier PDF individuel avec :
- Extraction du titre depuis le nom de fichier
- Détection automatique de la langue
- Recherche du nœud correspondant
- Import et attachement du fichier

#### `PdfZipProcessor::findNodeByTitle()`
Recherche intelligente de nœuds :
1. Recherche exacte
2. Recherche avec titre normalisé
3. Recherche approximative avec calcul de similarité

#### `PdfZipProcessor::detectLanguage()`
Détection de langue basée sur :
- Analyse des plages de caractères Unicode
- Calcul de pourcentages arabe/latin
- Seuils intelligents pour la classification

### Normalisation des caractères arabes
```php
// Hamza variants → ا
['أ', 'إ', 'آ'] → 'ا'

// Ya variants → ي  
['ي', 'ى'] → 'ي'

// Ta marbouta → ه
'ة' → 'ه'
```

## Gestion des erreurs

### Types d'erreurs gérées
- **Nœud non trouvé** : Suggestions de nœuds similaires
- **Fichier déjà existant** : Option d'écrasement
- **Erreur d'extraction ZIP** : Messages détaillés
- **Permissions de fichier** : Vérification automatique

### Logging
- Logs détaillés dans le channel `import_reglementation`
- Niveaux : INFO (succès), WARNING (ignorés), ERROR (échecs)
- Contexte complet pour le debugging

### Messages utilisateur
- **Succès** : Liste des fichiers importés avec nœuds associés
- **Avertissements** : Fichiers ignorés avec raisons
- **Erreurs** : Détails des échecs avec suggestions
- **Conseils** : Recommandations contextuelles

## Bonnes pratiques

### Préparation des données
1. **Vérifier les titres** : Correspondance exacte recommandée
2. **Nettoyer les noms** : Supprimer caractères spéciaux
3. **Encoder correctement** : UTF-8 pour l'arabe
4. **Tester d'abord** : Utiliser le mode simulation

### Optimisation des performances
- **Traitement par batch** : Évite les timeouts
- **Limitation mémoire** : Un PDF à la fois
- **Progression affichée** : Feedback utilisateur

### Maintenance
- **Surveiller les logs** : Identifier les patterns d'erreur
- **Nettoyer les fichiers temporaires** : Scripts de maintenance
- **Sauvegarder avant import** : Possibilité de rollback

## Dépannage

### Problèmes courants

#### "Aucun nœud trouvé"
- Vérifier la correspondance exacte des titres
- Utiliser le script de test pour la détection de langue
- Consulter les suggestions de nœuds similaires

#### "Extension ZipArchive non disponible"
```bash
sudo apt-get install php-zip
sudo systemctl restart apache2
```

#### "Permissions insuffisantes"
```bash
sudo chown -R www-data:www-data sites/default/files/
sudo chmod -R 755 sites/default/files/
```

#### "Caractères arabes corrompus"
- Vérifier l'encodage UTF-8 des noms de fichiers
- Utiliser des outils supportant l'Unicode pour créer le ZIP

### Debug avancé

#### Activer les logs de debug
```php
// settings.php
$config['system.logging']['error_level'] = 'verbose';
```

#### Tester la détection de langue
```php
$processor = \Drupal::service('import_reglementation.pdf_zip_processor');
$language = $processor->detectLanguage('عنوان المستند');
echo $language; // 'ar'
```

## Support et contribution

### Logs à vérifier
- `/admin/reports/dblog` : Messages d'erreur détaillés
- Channel `import_reglementation` : Logs spécifiques au module

### Fichiers de configuration
- `import_reglementation.routing.yml` : Routes du module
- `import_reglementation.services.yml` : Services enregistrés
- `import_reglementation.info.yml` : Métadonnées du module

---

**Version** : 2.0  
**Compatibilité** : Drupal 10+  
**Auteur** : Module personnalisé MTL  
**Dernière mise à jour** : 2024 