# Guide de dépannage - Import PDF ZIP

## ✅ Problèmes résolus

### 1. Erreur TypeError avec strlen()
**Symptôme:** `TypeError: strlen(): Argument #1 ($string) must be of type string, array given`

**✅ RÉSOLU:** Les messages d'affichage ont été corrigés pour utiliser `Markup::create()` au lieu d'arrays.

### 2. Limite de taille de fichier
**Symptôme:** "Le fichier transféré dépasse la taille de fichier maximum (2 Mo)"

**✅ RÉSOLU:** 
- Limite du module augmentée à **500 Mo**
- Validation corrigée (bytes au lieu de string)
- Outils de diagnostic créés

### 3. PDFs arabes assignés aux nœuds français
**Symptôme:** Les fichiers PDF avec contenu arabe sont incorrectement attachés aux nœuds français

**✅ RÉSOLU:**
- Logique de détection de langue améliorée
- Priorité donnée à la langue détectée automatiquement
- Logs détaillés pour traçabilité

---

## 🛠️ Outils de diagnostic

### 1. Script de vérification PHP
```bash
cd web/modules/custom/import_reglementation
php php_config_check.php
```
**Usage:** Vérifier que PHP peut supporter les fichiers de 500 Mo

### 2. Script de test de détection de langue
```bash
cd web/modules/custom/import_reglementation
php test_language_detection.php
```
**Usage:** Tester comment les titres sont analysés et quelle langue est détectée

### 3. Configuration automatique Apache
```bash
cd web/modules/custom/import_reglementation
sudo ./configure_php_apache.sh
```
**Usage:** Configurer automatiquement PHP pour Apache (environnement local)

---

## 🔍 Diagnostic des problèmes

### Problème: Import échoue
**Vérifications:**
1. ✅ Taille du fichier ZIP < 500 Mo
2. ✅ Paramètres PHP configurés (voir `php_config_check.php`)
3. ✅ Extensions PHP ZIP et FileInfo activées
4. ✅ Permissions d'écriture sur `public://imports/pdf_zip/`

### Problème: Mauvaise langue détectée
**Vérifications:**
1. ✅ Titres des PDFs contiennent des caractères arabes
2. ✅ Nœuds existent dans la langue appropriée
3. ✅ Tester avec `test_language_detection.php`
4. ✅ Consulter les logs dans `/admin/reports/dblog`

### Problème: PDF non attaché au bon nœud
**Vérifications:**
1. ✅ Titre du PDF correspond exactement au titre du nœud
2. ✅ Langue du nœud correspond à la langue détectée
3. ✅ Nœud de type "reglementation" existe
4. ✅ Option "Écraser les fichiers existants" si nécessaire

---

## 📊 Processus d'import amélioré

### 1. Analyse du fichier ZIP
- ✅ Validation du format ZIP
- ✅ Comptage des fichiers PDF
- ✅ Vérification de la taille (500 Mo max)

### 2. Traitement par fichier PDF
- ✅ Extraction du titre depuis le nom de fichier
- ✅ **Détection automatique de langue** (arabe/français)
- ✅ **Recherche prioritaire dans la langue détectée**
- ✅ Recherche fallback dans l'autre langue
- ✅ Normalisation des titres arabes

### 3. Assignation aux nœuds
- ✅ **Correspondance exacte par titre ET langue**
- ✅ Vérification des fichiers existants
- ✅ Logs détaillés pour traçabilité

---

## 📋 Critères de détection de langue

### Arabe détecté si:
- ✅ Plus de 30% de caractères arabes dans le titre
- ✅ Plus de caractères arabes que latins (si > 0)
- ✅ Caractères Unicode des plages arabes

### Normalisation arabe:
- ✅ Hamza: أ، إ، آ → ا
- ✅ Ya: ي، ى → ي  
- ✅ Ta marbouta: ة → ه

---

## 🚨 En cas d'échec

### 1. Vérifier les logs
```
/admin/reports/dblog (Drupal)
/var/log/apache2/error.log (Apache)
/var/log/php_errors.log (PHP)
```

### 2. Mode debug
Activer dans le formulaire:
- ✅ **Mode simulation** pour tester sans import réel
- ✅ Consulter la section "Informations système"

### 3. Test manuel
```bash
# Tester un titre spécifique
php test_language_detection.php

# Vérifier la configuration PHP
php php_config_check.php
```

---

## 📈 Statistiques d'import

Le formulaire affiche maintenant:
- ✅ **Nombre total** de fichiers traités
- ✅ **Succès** avec détails (fichier → nœud)
- ✅ **Erreurs** avec causes
- ✅ **Ignorés** (fichiers existants)
- ✅ **Conseils** personnalisés selon les résultats

---

## 🔧 Configuration serveur de production (Nginx)

### Fichiers à modifier:
1. **PHP-FPM:** `/etc/php/8.3/fpm/php.ini`
2. **Nginx:** `/etc/nginx/sites-available/votre-site`

### Paramètres PHP-FPM:
```ini
upload_max_filesize = 500M
post_max_size = 500M
max_execution_time = 600
memory_limit = 1G
```

### Configuration Nginx:
```nginx
client_max_body_size 500M;
client_body_timeout 600s;
fastcgi_read_timeout 600s;
```

### Redémarrage:
```bash
sudo systemctl restart php8.3-fpm
sudo systemctl restart nginx
```

---

## 📞 Support

Si les problèmes persistent:
1. ✅ Exécuter tous les scripts de diagnostic
2. ✅ Consulter les logs système
3. ✅ Vérifier la correspondance exacte des titres
4. ✅ Tester avec un fichier ZIP plus petit d'abord 