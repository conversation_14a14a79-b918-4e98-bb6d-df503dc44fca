<?php

/**
 * Script pour analyser les titres des PDFs dans un ZIP
 * Usage: drush php:script analyze_zip_titles.php [chemin_vers_fichier.zip]
 */

use Drupal\node\Entity\Node;

echo "=== Recherche de fichiers ZIP ===\n";

// Chercher automatiquement les fichiers ZIP
$possible_paths = [
  'sites/default/files/imports/pdf_zip/',
  'web/sites/default/files/imports/pdf_zip/',
  'sites/default/files/',
  'web/sites/default/files/',
  './'
];

$zip_path = null;

// Si un chemin est fourni en argument
if (!empty($argv[1]) && $argv[1] !== 'auto') {
  if (file_exists($argv[1])) {
    $zip_path = $argv[1];
    echo "📁 Utilisation du fichier spécifié: " . basename($zip_path) . "\n\n";
  }
}

// Sinon, chercher automatiquement
if (!$zip_path) {
  echo "🔍 Recherche automatique de fichiers ZIP...\n";
  
  foreach ($possible_paths as $dir) {
    if (is_dir($dir)) {
      $files = glob($dir . '*.zip');
      if (!empty($files)) {
        // Trier par date de modification (plus récent en premier)
        usort($files, function($a, $b) {
          return filemtime($b) - filemtime($a);
        });
        $zip_path = $files[0];
        echo "📁 Fichier ZIP trouvé: " . basename($zip_path) . " dans $dir\n\n";
        break;
      }
    }
  }
}

if (!$zip_path) {
  echo "❌ Aucun fichier ZIP trouvé.\n";
  echo "Placez votre fichier ZIP dans un de ces répertoires:\n";
  foreach ($possible_paths as $dir) {
    echo "- $dir\n";
  }
  echo "\nOu spécifiez le chemin: drush php:script analyze_zip_titles.php /chemin/vers/fichier.zip\n";
  exit;
}

echo "=== Analyse du fichier ZIP ===\n";
echo "Fichier: " . basename($zip_path) . "\n";
echo "Chemin: $zip_path\n\n";

// Ouvrir le ZIP
$zip = new ZipArchive();
$result = $zip->open($zip_path);

if ($result !== TRUE) {
  echo "❌ Impossible d'ouvrir le ZIP. Code d'erreur: $result\n";
  exit;
}

// Extraire tous les titres de PDFs
$pdf_titles = [];
for ($i = 0; $i < $zip->numFiles; $i++) {
  $filename = $zip->getNameIndex($i);
  if (preg_match('/\.pdf$/i', $filename) && !preg_match('/\/$/', $filename)) {
    $title = preg_replace('/\.(pdf|PDF)$/', '', basename($filename));
    $pdf_titles[] = [
      'filename' => $filename,
      'title' => $title,
    ];
  }
}

$zip->close();

echo "📋 Trouvé " . count($pdf_titles) . " fichier(s) PDF\n\n";

// Fonction pour détecter la langue
function detectLanguage($title) {
  $clean_title = preg_replace('/[\s\p{P}]/u', '', $title);
  
  if (empty($clean_title)) {
    return 'fr';
  }
  
  $arabic_patterns = [
    '[\x{0600}-\x{06FF}]',
    '[\x{0750}-\x{077F}]',
    '[\x{08A0}-\x{08FF}]',
    '[\x{FB50}-\x{FDFF}]',
    '[\x{FE70}-\x{FEFF}]',
  ];
  
  $arabic_chars = 0;
  foreach ($arabic_patterns as $pattern) {
    $arabic_chars += preg_match_all('/' . $pattern . '/u', $clean_title);
  }
  
  $latin_chars = preg_match_all('/[a-zA-ZÀ-ÿ]/u', $clean_title);
  $total_chars = mb_strlen($clean_title, 'UTF-8');
  $arabic_percent = $total_chars > 0 ? ($arabic_chars / $total_chars) * 100 : 0;
  
  return ($arabic_percent > 30 || ($arabic_chars > 0 && $arabic_chars >= $latin_chars)) ? 'ar' : 'fr';
}

// Analyser la langue des PDFs
$arabic_pdfs = [];
$french_pdfs = [];

foreach ($pdf_titles as $pdf) {
  $lang = detectLanguage($pdf['title']);
  if ($lang === 'ar') {
    $arabic_pdfs[] = $pdf;
  } else {
    $french_pdfs[] = $pdf;
  }
}

echo "=== Répartition par langue détectée ===\n";
echo "🇸🇦 PDFs arabes: " . count($arabic_pdfs) . "\n";
echo "🇫🇷 PDFs français: " . count($french_pdfs) . "\n\n";

// Charger les nœuds arabes existants
$query = \Drupal::entityQuery('node')
  ->condition('type', 'reglementation')
  ->condition('langcode', 'ar')
  ->condition('status', 1)
  ->accessCheck(FALSE);

$nids = $query->execute();
$arabic_nodes = [];

if (!empty($nids)) {
  $nodes = Node::loadMultiple($nids);
  foreach ($nodes as $node) {
    $arabic_nodes[] = [
      'nid' => $node->id(),
      'title' => $node->getTitle(),
      'has_pdf' => !$node->get('field_lien_telechargement')->isEmpty(),
    ];
  }
}

echo "=== Nœuds arabes existants (" . count($arabic_nodes) . ") ===\n";
foreach ($arabic_nodes as $node) {
  $status = $node['has_pdf'] ? '📎' : '📄';
  echo "$status #" . $node['nid'] . ": " . $node['title'] . "\n";
}

echo "\n=== Analyse des PDFs arabes ===\n";

$matches = [];
$no_matches = [];

foreach ($arabic_pdfs as $pdf) {
  echo "📄 " . $pdf['title'] . "\n";
  
  // Chercher une correspondance exacte
  $found = false;
  foreach ($arabic_nodes as $node) {
    if ($pdf['title'] === $node['title']) {
      $matches[] = [
        'pdf' => $pdf['title'],
        'node' => $node['title'],
        'nid' => $node['nid'],
        'type' => 'exact'
      ];
      $found = true;
      echo "   ✅ Correspondance exacte: Nœud #" . $node['nid'] . "\n";
      break;
    }
  }
  
  if (!$found) {
    // Chercher une correspondance approximative
    foreach ($arabic_nodes as $node) {
      similar_text($pdf['title'], $node['title'], $percent);
      if ($percent > 80) {
        $matches[] = [
          'pdf' => $pdf['title'],
          'node' => $node['title'],
          'nid' => $node['nid'],
          'type' => 'similar',
          'similarity' => $percent
        ];
        $found = true;
        echo "   🟡 Correspondance approximative (" . round($percent, 1) . "%): Nœud #" . $node['nid'] . "\n";
        echo "      Nœud: " . $node['title'] . "\n";
        break;
      }
    }
  }
  
  if (!$found) {
    $no_matches[] = $pdf['title'];
    echo "   ❌ Aucune correspondance trouvée\n";
  }
  
  echo "\n";
}

echo "=== Résumé ===\n";
echo "📊 Total PDFs arabes: " . count($arabic_pdfs) . "\n";
echo "✅ Correspondances exactes: " . count(array_filter($matches, function($m) { return $m['type'] === 'exact'; })) . "\n";
echo "🟡 Correspondances approximatives: " . count(array_filter($matches, function($m) { return $m['type'] === 'similar'; })) . "\n";
echo "❌ Nœuds arabes manquants: " . count($no_matches) . "\n\n";

if (!empty($no_matches)) {
  echo "=== 🚨 SOLUTION: Nœuds arabes à créer ===\n";
  echo "Vous devez créer ces " . count($no_matches) . " nœud(s) en langue arabe:\n\n";
  
  foreach ($no_matches as $i => $title) {
    echo ($i + 1) . ". $title\n";
  }
  
  echo "\n🔧 ÉTAPES POUR RÉSOUDRE LE PROBLÈME:\n";
  echo "1. Aller dans votre interface Drupal: Contenu → Ajouter du contenu → Réglementation\n";
  echo "2. Choisir la langue 'العربية' (Arabe) en haut du formulaire\n";
  echo "3. Copier-coller chaque titre ci-dessus EXACTEMENT comme écrit\n";
  echo "4. Remplir les autres champs requis\n";
  echo "5. Publier le nœud\n";
  echo "6. Répéter pour tous les titres\n";
  echo "7. Relancer l'import avec langue 'Arabe' et 'Écraser les fichiers'\n\n";
  
  echo "📋 COPIER-COLLER (un par un dans Drupal):\n";
  echo str_repeat("=", 50) . "\n";
  foreach ($no_matches as $title) {
    echo "$title\n";
  }
  echo str_repeat("=", 50) . "\n";
}

if (count($arabic_pdfs) > 0 && count($no_matches) === 0) {
  echo "🎉 Tous les PDFs arabes ont des nœuds correspondants!\n";
  echo "Le problème pourrait être ailleurs. Vérifiez:\n";
  echo "- Que les nœuds sont bien publiés\n";
  echo "- Que l'option 'Écraser les fichiers existants' est cochée si nécessaire\n";
} 