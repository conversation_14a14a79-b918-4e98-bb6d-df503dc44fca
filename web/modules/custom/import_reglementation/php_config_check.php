<?php
/**
 * Script de diagnostic pour vérifier les paramètres PHP
 * pour l'upload de fichiers ZIP dans le module import_reglementation.
 * 
 * Utilisez ce script pour vérifier si votre serveur peut supporter
 * l'upload de fichiers ZIP jusqu'à 500 Mo.
 * 
 * Usage: php php_config_check.php
 */

/**
 * Convertit une valeur de taille PHP en bytes.
 */
function convertToBytes($val) {
    if (empty($val)) return 0;
    
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int) $val;
    
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    
    return $val;
}

echo "=== Diagnostic des paramètres PHP pour l'upload de fichiers ZIP ===\n\n";

// Paramètres à vérifier
$required_params = [
    'upload_max_filesize' => '500M',
    'post_max_size' => '500M',
    'max_file_uploads' => '20',
    'max_execution_time' => '600',
    'max_input_time' => '600',
    'memory_limit' => '1G'
];

echo "Paramètres actuels:\n";
echo "-------------------\n";

$issues = [];

foreach ($required_params as $param => $recommended) {
    $current = ini_get($param);
    $status = '✓';
    
    // Convertir les valeurs en bytes pour comparaison
    $current_bytes = $param === 'max_file_uploads' || $param === 'max_execution_time' || $param === 'max_input_time' 
        ? (int)$current 
        : convertToBytes($current);
    
    $recommended_bytes = $param === 'max_file_uploads' || $param === 'max_execution_time' || $param === 'max_input_time'
        ? (int)$recommended
        : convertToBytes($recommended);
    
    if ($current_bytes < $recommended_bytes) {
        $status = '❌';
        $issues[] = "$param: $current (recommandé: $recommended)";
    }
    
    printf("%-20s: %-10s %s\n", $param, $current, $status);
}

echo "\n";

if (empty($issues)) {
    echo "✅ Tous les paramètres PHP sont correctement configurés!\n";
    echo "Votre serveur peut supporter l'upload de fichiers ZIP jusqu'à 500 Mo.\n";
} else {
    echo "❌ Problèmes détectés:\n";
    echo "---------------------\n";
    foreach ($issues as $issue) {
        echo "• $issue\n";
    }
    
    echo "\nPour corriger ces problèmes:\n";
    echo "1. Modifiez votre fichier php.ini\n";
    echo "2. Redémarrez votre serveur web\n";
    echo "3. Relancez ce script pour vérifier\n\n";
    
    echo "Exemple de configuration php.ini:\n";
    echo "---------------------------------\n";
    foreach ($required_params as $param => $value) {
        echo "$param = $value\n";
    }
}

echo "\n";
echo "Extensions PHP requises:\n";
echo "------------------------\n";

$required_extensions = ['zip', 'fileinfo'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅' : '❌';
    echo "$ext: $status\n";
    
    if (!$loaded) {
        $issues[] = "Extension $ext non disponible";
    }
}

echo "\nInformations système:\n";
echo "---------------------\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Serveur: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'CLI') . "\n";
echo "OS: " . PHP_OS . "\n";

if (!empty($issues)) {
    echo "\n⚠️  Contactez votre administrateur système pour résoudre ces problèmes.\n";
} 