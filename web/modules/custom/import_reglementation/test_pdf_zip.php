<?php

/**
 * @file
 * Script de test pour valider l'import PDF ZIP.
 * 
 * Usage:
 * php test_pdf_zip.php
 */

use Drupal\Core\DrupalKernel;
use Drupal\Core\Site\Settings;
use Symfony\Component\HttpFoundation\Request;

// Configuration Drupal
$autoloader = require_once 'web/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$kernel->setSitePath('sites/default');
Settings::initialize(dirname(__DIR__), 'sites/default', $autoloader);

$request = Request::createFromGlobals();
$kernel->boot();
$kernel->preHandle($request);

// Services
$pdf_processor = \Drupal::service('import_reglementation.pdf_zip_processor');
$entity_manager = \Drupal::entityTypeManager();

echo "=== Test du module Import PDF ZIP ===\n\n";

// Test 1: Vérifier la détection de langue
echo "1. Test de détection de langue:\n";
$test_titles = [
  'قانون المالية' => 'ar',
  'Code de la route' => 'fr',
  'Loi organique رقم 123' => 'ar',
  'Règlement intérieur' => 'fr',
  'الدستور المغربي' => 'ar',
];

foreach ($test_titles as $title => $expected) {
  $detected = $pdf_processor->detectLanguage($title);
  $status = ($detected === $expected) ? '✓' : '✗';
  echo "  $status '$title' → $detected ($expected attendu)\n";
}

echo "\n";

// Test 2: Vérifier les nœuds de réglementation existants
echo "2. Nœuds de réglementation dans la base:\n";
$query = $entity_manager->getStorage('node')->getQuery()
  ->accessCheck(FALSE)
  ->condition('type', 'reglementation')
  ->range(0, 10);

$nids = $query->execute();

if (empty($nids)) {
  echo "  ⚠ Aucun nœud de réglementation trouvé\n";
  echo "  Conseil: Créez quelques nœuds pour tester l'import\n";
} else {
  $nodes = $entity_manager->getStorage('node')->loadMultiple($nids);
  foreach ($nodes as $node) {
    $lang = $node->language()->getId();
    $has_pdf = !$node->get('field_lien_telechargement')->isEmpty();
    $pdf_status = $has_pdf ? '📄' : '⚪';
    echo "  $pdf_status Nœud #{$node->id()}: '{$node->getTitle()}' ($lang)\n";
  }
  echo "  Total: " . count($nids) . " nœud(s) trouvé(s)\n";
}

echo "\n";

// Test 3: Vérifier les permissions du répertoire
echo "3. Test des permissions:\n";
$upload_dir = 'sites/default/files/imports/pdf_zip/';
$destination_dir = 'sites/default/files/reglementation/pdf/';

foreach ([$upload_dir, $destination_dir] as $dir) {
  if (!is_dir($dir)) {
    if (mkdir($dir, 0755, true)) {
      echo "  ✓ Répertoire créé: $dir\n";
    } else {
      echo "  ✗ Impossible de créer: $dir\n";
    }
  } else {
    $writable = is_writable($dir);
    $status = $writable ? '✓' : '✗';
    echo "  $status Répertoire $dir " . ($writable ? 'accessible' : 'non accessible') . "\n";
  }
}

echo "\n";

// Test 4: Simuler une recherche de nœud
echo "4. Test de recherche de nœuds:\n";
if (!empty($nodes)) {
  $test_node = reset($nodes);
  $title = $test_node->getTitle();
  $lang = $test_node->language()->getId();
  
  echo "  Test avec le titre: '$title' (langue: $lang)\n";
  
  // Test de recherche exacte
  $found = $pdf_processor->findNodeByTitle($title, $lang);
  $status = $found ? '✓' : '✗';
  echo "  $status Recherche exacte: " . ($found ? "trouvé (#{$found->id()})" : "non trouvé") . "\n";
  
  // Test de recherche approximative
  $modified_title = $title . " modifié";
  $found_approx = $pdf_processor->findNodeByTitle($modified_title, $lang);
  $status = $found_approx ? '✓' : '✗';
  echo "  $status Recherche approximative '$modified_title': " . ($found_approx ? "trouvé (#{$found_approx->id()})" : "non trouvé") . "\n";
}

echo "\n";

// Test 5: Vérifier les services nécessaires
echo "5. Services Drupal:\n";
$services_to_check = [
  'import_reglementation.pdf_zip_processor',
  'entity_type.manager',
  'file_system',
  'logger.factory',
  'messenger',
];

foreach ($services_to_check as $service_id) {
  try {
    $service = \Drupal::service($service_id);
    echo "  ✓ Service $service_id: OK\n";
  } catch (\Exception $e) {
    echo "  ✗ Service $service_id: ERREUR - {$e->getMessage()}\n";
  }
}

echo "\n";

// Conseils d'utilisation
echo "=== Conseils d'utilisation ===\n";
echo "1. Accédez au formulaire: /admin/content/import-pdf-zip\n";
echo "2. Assurez-vous que les titres des PDFs correspondent aux titres des nœuds\n";
echo "3. Pour l'arabe, utilisez l'encodage UTF-8 dans les noms de fichiers\n";
echo "4. Testez d'abord en mode simulation avant l'import réel\n";
echo "5. Consultez les logs Drupal en cas de problème\n";

echo "\n=== Test terminé ===\n"; 