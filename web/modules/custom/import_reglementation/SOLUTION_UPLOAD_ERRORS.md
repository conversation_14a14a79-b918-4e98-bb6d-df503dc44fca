# Solution aux erreurs d'upload de fichiers PDF

## Problème identifié

L'erreur suivante se produisait lors de l'upload de fichiers PDF avec des noms en arabe :

```
Erreur de transfert. Impossible de déplacer le fichier transféré vers la destination
Warning: move_uploaded_file(): Unable to move "/tmp/phpXXXXXX" to "/var/www/html/mtl/web/sites/default/files/import_reglementation_uploads/..."
```

## Causes du problème

1. **Permissions insuffisantes** : Le répertoire `import_reglementation_uploads` avait des permissions `755` au lieu de `775`
2. **Noms de fichiers en arabe** : Les caractères arabes dans les noms de fichiers causaient des problèmes d'encodage
3. **Gestion des erreurs insuffisante** : Le code ne gérait pas correctement les erreurs d'upload

## Solutions implémentées

### 1. Correction des permissions

```bash
# Corriger les permissions du répertoire
sudo chmod 775 /var/www/html/mtl/web/sites/default/files/import_reglementation_uploads

# S'assurer que l'utilisateur fait partie du groupe www-data
sudo usermod -a -G www-data farouk
```

### 2. Amélioration de la sanitisation des noms de fichiers

- **Avant** : Sanitisation basique qui ne gérait pas bien les caractères arabes
- **Après** : Sanitisation avancée qui :
  - Extrait les numéros de référence avec plusieurs patterns
  - Génère des noms de fichiers ASCII seulement
  - Ajoute un timestamp pour éviter les conflits
  - Gère mieux les cas d'erreur

### 3. Amélioration de la gestion des permissions

Le code vérifie maintenant automatiquement :
- L'existence du répertoire
- Les permissions correctes (775)
- L'accessibilité en écriture
- Log les erreurs pour debugging

### 4. Amélioration de la gestion des erreurs d'upload

Le `valueCallback` personnalisé :
- Log les erreurs d'upload avec des messages explicites
- Gère les différents codes d'erreur PHP
- Fournit un debugging détaillé

## Code modifié

### ImportPdfZipForm.php

1. **buildForm()** : Amélioration de la préparation du répertoire
2. **sanitizeFilename()** : Sanitisation plus robuste
3. **valueCallback()** : Gestion d'erreurs améliorée

## Test de la solution

Pour vérifier que la solution fonctionne :

1. Vérifier les permissions :
   ```bash
   ls -la /var/www/html/mtl/web/sites/default/files/import_reglementation_uploads/
   ```

2. Tester l'upload de fichiers avec des noms en arabe

3. Vérifier les logs Drupal pour les messages de debugging

## Prévention

Pour éviter ce problème à l'avenir :

1. **Permissions** : S'assurer que les répertoires d'upload ont les permissions `775`
2. **Propriétaire** : S'assurer que les répertoires appartiennent à `www-data:www-data`
3. **Sanitisation** : Toujours sanitiser les noms de fichiers uploadés
4. **Monitoring** : Surveiller les logs pour détecter les erreurs d'upload

## Commandes utiles

```bash
# Vérifier les permissions
ls -la /var/www/html/mtl/web/sites/default/files/

# Corriger les permissions si nécessaire
sudo chmod 775 /var/www/html/mtl/web/sites/default/files/import_reglementation_uploads
sudo chown -R www-data:www-data /var/www/html/mtl/web/sites/default/files/import_reglementation_uploads

# Vérifier les groupes de l'utilisateur
groups

# Ajouter l'utilisateur au groupe www-data
sudo usermod -a -G www-data $USER
``` 