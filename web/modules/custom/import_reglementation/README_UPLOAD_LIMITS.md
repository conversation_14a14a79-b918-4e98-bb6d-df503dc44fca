# Résolution des problèmes de taille de fichier pour l'import PDF ZIP

## Problème courant

Si vous rencontrez l'erreur "Une erreur irrécupérable s'est produite. Le fichier transféré dépasse la taille de fichier maximum (2 Mo) autorisée par le serveur", c'est que les paramètres PHP de votre serveur limitent la taille des fichiers uploadés.

## Solution rapide

Le module a été configuré pour accepter des fichiers ZIP jusqu'à **500 Mo**, mais votre serveur doit également être configuré pour supporter cette taille.

## Vérification des paramètres

### Méthode 1: Script de diagnostic automatique
Exécutez le script de diagnostic inclus:
```bash
cd web/modules/custom/import_reglementation/
php php_config_check.php
```

### Méthode 2: Vérification manuelle
Vérifiez les paramètres suivants dans votre `php.ini`:

```ini
upload_max_filesize = 500M
post_max_size = 500M
max_file_uploads = 20
max_execution_time = 600
max_input_time = 600
memory_limit = 1G
```

## Configuration requise

### Paramètres PHP essentiels

| Paramètre | Valeur recommandée | Description |
|-----------|-------------------|-------------|
| `upload_max_filesize` | 500M | Taille maximum d'un fichier uploadé |
| `post_max_size` | 500M | Taille maximum des données POST |
| `max_file_uploads` | 20 | Nombre maximum de fichiers simultanés |
| `max_execution_time` | 600 | Temps d'exécution maximum (10 min) |
| `max_input_time` | 600 | Temps d'analyse des données d'entrée |
| `memory_limit` | 1G | Limite mémoire PHP |

### Extensions PHP requises

- `zip` - pour l'extraction des fichiers ZIP
- `fileinfo` - pour la détection des types de fichiers

## Étapes de configuration

### 1. Localiser le fichier php.ini

```bash
# Trouver l'emplacement du php.ini
php --ini

# Ou afficher la configuration
php -i | grep "Configuration File"
```

### 2. Modifier les paramètres

Éditez votre `php.ini` et ajustez les valeurs:

```ini
# Tailles de fichiers
upload_max_filesize = 500M
post_max_size = 500M

# Limites de traitement
max_file_uploads = 20
max_execution_time = 600
max_input_time = 600
memory_limit = 1G
```

### 3. Redémarrer le serveur web

```bash
# Apache
sudo systemctl restart apache2

# Nginx + PHP-FPM
sudo systemctl restart nginx
sudo systemctl restart php7.4-fpm  # Ajustez la version PHP

# Pour XAMPP/WAMP/MAMP
# Redémarrer via l'interface graphique
```

### 4. Vérifier les modifications

Revenez au formulaire d'import et regardez dans "Informations système" (section dépliable) pour voir les nouvelles limites.

## Configuration serveur web

### Apache (.htaccess)

Si vous ne pouvez pas modifier php.ini, essayez d'ajouter dans votre `.htaccess`:

```apache
php_value upload_max_filesize 500M
php_value post_max_size 500M
php_value max_execution_time 600
php_value max_input_time 600
php_value memory_limit 1G
```

### Nginx

Pour Nginx, les paramètres PHP doivent être configurés dans le pool PHP-FPM, pas dans la configuration Nginx.

## Dépannage avancé

### Vérifier les logs

1. **Logs PHP** : Vérifiez `/var/log/php_errors.log` ou `/var/log/apache2/error.log`
2. **Logs Drupal** : Allez dans `/admin/reports/dblog` dans votre site Drupal

### Tester avec un fichier plus petit

1. Créez un fichier ZIP de test < 2 Mo
2. Si cela fonctionne, le problème est bien la taille
3. Augmentez progressivement la taille pour tester

### Environnements spécifiques

#### Hébergement partagé
- Contactez votre hébergeur pour augmenter les limites
- Certains hébergeurs permettent la modification via cPanel

#### Docker/Conteneurs
```dockerfile
# Dans votre Dockerfile PHP
RUN echo "upload_max_filesize = 500M" >> /usr/local/etc/php/conf.d/uploads.ini && \
    echo "post_max_size = 500M" >> /usr/local/etc/php/conf.d/uploads.ini
```

#### XAMPP/WAMP
1. Ouvrir le panneau de contrôle
2. Cliquer sur "Config" → "PHP (php.ini)"
3. Modifier les valeurs
4. Redémarrer Apache

## Vérification finale

Une fois configuré, le formulaire d'import affichera dans "Informations système" les nouvelles limites. Vous devriez voir:

```
upload_max_filesize = 500M
post_max_size = 500M
```

## Support

Si le problème persiste:

1. Exécutez le diagnostic: `php php_config_check.php`
2. Vérifiez les logs serveur
3. Contactez votre administrateur système si nécessaire

## Sécurité

**Important**: Ces paramètres affectent l'ensemble de votre site PHP. Assurez-vous que:

- L'accès au formulaire d'import est restreint aux administrateurs
- Votre serveur dispose de suffisamment d'espace disque
- Les timeouts ne perturbent pas les autres applications 