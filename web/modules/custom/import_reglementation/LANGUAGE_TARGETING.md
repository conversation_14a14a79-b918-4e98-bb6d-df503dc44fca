# Ciblage de langue pour l'attachement de fichiers PDF

## Problème résolu

**Avant** : Les fichiers PDF étaient attachés à **toutes** les traductions d'un nœud (français ET arabe).

**Après** : Les fichiers PDF sont attachés **uniquement** à la langue du PDF ou celle choisie dans le sélecteur.

## Comment ça fonctionne

### 1. Détection/Sélection de la langue

- **Langue forcée** : Si une langue est sélectionnée dans le formulaire, elle est utilisée
- **Détection automatique** : Sinon, la langue est détectée automatiquement basée sur le contenu du titre du fichier

### 2. Ciblage de la traduction

Le système :
1. Détermine la langue cible (forcée ou détectée)
2. Vérifie si cette langue existe dans les traductions du nœud
3. Attache le fichier **uniquement** à cette traduction spécifique
4. Si la langue n'existe pas, attache au nœud original avec un avertissement

### 3. Exemples

#### Fichier avec nom en arabe
```
Nom: قرار لوزير التجهيز والنقل واللوجيستيك رقم 061.16.pdf
Langue détectée: ar
Résultat: Fichier attaché uniquement à la traduction arabe
```

#### Fichier avec nom en français
```
Nom: Arrêté du ministre de l'équipement numéro 061.16.pdf
Langue détectée: fr
Résultat: Fichier attaché uniquement à la traduction française
```

#### Langue forcée dans le formulaire
```
Sélecteur: "Français" (même si le nom est en arabe)
Résultat: Fichier attaché uniquement à la traduction française
```

## Code modifié

### Fichier : `PdfZipProcessor.php`

**Fonction** : `processPdfFromUrl()`

**Changements principaux** :

1. **Avant** (lignes ~1110-1160) :
   ```php
   // Attacher le fichier à toutes les traductions du nœud
   foreach ($translations as $langcode => $language_object) {
     // ... attacher à chaque traduction
   }
   ```

2. **Après** :
   ```php
   // Déterminer la langue cible pour l'attachement du fichier
   $target_language = $detected_language;
   
   // Vérifier si la langue cible existe dans les traductions
   if (in_array($target_language, $available_languages)) {
     // Attacher le fichier à la traduction spécifique
     $target_node = $node->getTranslation($target_language);
     // ... attacher uniquement à cette traduction
   }
   ```

## Avantages

1. **Précision** : Chaque fichier PDF est dans la bonne langue
2. **Clarté** : Plus de confusion sur quelle version télécharger
3. **Maintenance** : Plus facile de gérer les fichiers par langue
4. **Performance** : Moins de fichiers dupliqués

## Messages de log

Le système log maintenant :
- La langue cible déterminée
- Les langues disponibles pour le nœud
- La traduction utilisée pour l'attachement
- Les avertissements si la langue cible n'existe pas

## Gestion des cas particuliers

### Cas 1 : Langue cible n'existe pas
```
Langue détectée: ar
Traductions disponibles: fr seulement
Action: Attacher au nœud original (fr) avec avertissement
```

### Cas 2 : Nœud sans traductions
```
Traductions disponibles: fr seulement (pas de traductions)
Action: Attacher au nœud original
```

### Cas 3 : Fichier déjà existant
```
Overwrite: désactivé
Fichier existant: oui
Action: Ignorer avec message "déjà attaché"
```

## Test

Pour tester la fonctionnalité :

1. **Upload d'un fichier avec nom arabe** → Doit être attaché à la traduction arabe
2. **Upload d'un fichier avec nom français** → Doit être attaché à la traduction française  
3. **Sélection manuelle de langue** → Doit respecter la sélection
4. **Vérification dans l'interface** → Chaque traduction a son propre fichier

## Compatibilité

Cette modification est **rétrocompatible** :
- Les fichiers existants ne sont pas affectés
- Les nœuds sans traductions continuent de fonctionner
- L'interface utilisateur reste identique 