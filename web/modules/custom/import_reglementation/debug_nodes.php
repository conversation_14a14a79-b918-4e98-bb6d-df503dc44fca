<?php

/**
 * Script simple pour lister les nœuds de réglementation
 * Usage: drush php:script debug_nodes.php
 */

use Drupal\node\Entity\Node;

echo "=== Liste des nœuds de réglementation ===\n\n";

// Charger tous les nœuds de type réglementation
$query = \Drupal::entityQuery('node')
  ->condition('type', 'reglementation')
  ->condition('status', 1)
  ->accessCheck(FALSE);

$nids = $query->execute();

if (empty($nids)) {
  echo "❌ Aucun nœud de réglementation trouvé !\n";
  exit;
}

echo "✅ Trouvé " . count($nids) . " nœud(s) de réglementation\n\n";

$nodes = Node::loadMultiple($nids);

$by_language = [];
foreach ($nodes as $node) {
  $lang = $node->language()->getId();
  $title = $node->getTitle();
  
  if (!isset($by_language[$lang])) {
    $by_language[$lang] = [];
  }
  
  $by_language[$lang][] = [
    'nid' => $node->id(),
    'title' => $title,
    'has_pdf' => !$node->get('field_lien_telechargement')->isEmpty(),
  ];
}

foreach ($by_language as $lang => $nodes_list) {
  $lang_name = $lang === 'ar' ? '🇸🇦 Arabe' : '🇫🇷 Français';
  echo "=== $lang_name ($lang) - " . count($nodes_list) . " nœud(s) ===\n";
  
  foreach ($nodes_list as $node_info) {
    $pdf_status = $node_info['has_pdf'] ? '📎 PDF attaché' : '📄 Pas de PDF';
    echo sprintf(
      "- #%d: \"%s\" [%s]\n",
      $node_info['nid'],
      mb_substr($node_info['title'], 0, 80) . (mb_strlen($node_info['title']) > 80 ? '...' : ''),
      $pdf_status
    );
  }
  echo "\n";
}

echo "=== Analyse ===\n";
echo "Total nœuds: " . count($nids) . "\n";
echo "Répartition par langue:\n";
foreach ($by_language as $lang => $nodes_list) {
  $with_pdf = array_filter($nodes_list, function($n) { return $n['has_pdf']; });
  $without_pdf = array_filter($nodes_list, function($n) { return !$n['has_pdf']; });
  
  $lang_name = $lang === 'ar' ? 'Arabe' : 'Français';
  echo "- $lang_name: " . count($nodes_list) . " (" . count($with_pdf) . " avec PDF, " . count($without_pdf) . " sans PDF)\n";
}

echo "\n=== Recommandations ===\n";
echo "1. Vérifiez que les titres des PDFs correspondent exactement aux titres des nœuds\n";
echo "2. Pour les fichiers ignorés, activez 'Écraser les fichiers existants' si vous voulez les remplacer\n";
echo "3. Pour les erreurs, vérifiez si les nœuds existent avec des titres légèrement différents\n"; 