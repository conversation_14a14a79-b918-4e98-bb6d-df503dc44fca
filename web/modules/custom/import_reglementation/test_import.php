<?php

/**
 * Script de test pour l'importation CSV - Version simplifiée
 */

echo "=== Test d'importation CSV ===\n";

// Chemin vers le fichier CSV de test
$csv_file = __DIR__ . '/csv/test_small.csv';

echo "Fichier CSV: $csv_file\n";

// Vérifier que le fichier existe
if (!file_exists($csv_file)) {
    echo "ERREUR: Le fichier CSV n'existe pas!\n";
    exit(1);
}

// Lire le fichier CSV
$handle = fopen($csv_file, 'r');
if ($handle === FALSE) {
    echo "ERREUR: Impossible d'ouvrir le fichier CSV!\n";
    exit(1);
}

// Lire l'en-tête
$header = fgetcsv($handle, 0, ',');
echo "En-tête CSV: " . implode(' | ', $header) . "\n";

// Lire toutes les lignes de données pour tester les cas de fallback
$line_number = 1;
while (($data = fgetcsv($handle, 0, ',')) !== FALSE) {
    echo "\n--- Ligne $line_number ---\n";
    echo "Données brutes: " . implode(' | ', $data) . "\n";

    // Créer un tableau associatif
    $row = array_combine($header, $data);

    // Tester la logique de fallback pour les titres
    $title_fr = $row['Intitulé en français'];
    $title_ar = $row['Intitulé en arabe'];
    $numero = $row['N° du texte'];

    // Logique de fallback pour le français
    if (empty($title_fr) || $title_fr === '-' || trim($title_fr) === '') {
        $final_title_fr = $numero;
        echo "  Titre FR (fallback): $final_title_fr (était: '$title_fr')\n";
    } else {
        $final_title_fr = $title_fr;
        echo "  Titre FR (normal): $final_title_fr\n";
    }

    // Logique de fallback pour l'arabe
    if (empty($title_ar) || $title_ar === '-' || trim($title_ar) === '') {
        $final_title_ar = $numero;
        echo "  Titre AR (fallback): $final_title_ar (était: '$title_ar')\n";
    } else {
        $final_title_ar = $title_ar;
        echo "  Titre AR (normal): $final_title_ar\n";
    }

    echo "  Numéro: $numero\n";
    echo "  Date: " . $row['Date de Publication'] . "\n";

    $line_number++;
}

if ($line_number === 1) {
    echo "ERREUR: Aucune donnée trouvée!\n";
}

fclose($handle);

echo "\n=== Test terminé ===\n";
