<?php

/**
 * Script pour comparer un titre PDF spécifique avec les nœuds existants
 * Usage: drush php:script compare_titles.php "titre du PDF"
 * Exemple: drush php:script compare_titles.php "قانون رقم 123 لسنة 2023"
 */

use Drupal\node\Entity\Node;

// Récupérer le titre depuis les arguments de ligne de commande
$title_to_search = $argv[1] ?? null;

if (!$title_to_search) {
  echo "❌ Usage: drush php:script compare_titles.php \"titre du PDF\"\n";
  echo "📋 Titres disponibles dans le ZIP:\n\n";
  
  // Afficher la liste des titres disponibles
  $zip_path = null;
  $possible_paths = [
    'sites/default/files/imports/pdf_zip/',
    'web/sites/default/files/imports/pdf_zip/',
    'sites/default/files/',
    'web/sites/default/files/',
  ];

  foreach ($possible_paths as $dir) {
    if (is_dir($dir)) {
      $files = glob($dir . '*.zip');
      if (!empty($files)) {
        usort($files, function($a, $b) {
          return filemtime($b) - filemtime($a);
        });
        $zip_path = $files[0];
        break;
      }
    }
  }

  if ($zip_path) {
    echo "📁 Fichier ZIP: " . basename($zip_path) . "\n\n";
    $zip = new ZipArchive();
    $zip->open($zip_path);
    
    for ($i = 0; $i < $zip->numFiles; $i++) {
      $filename = $zip->getNameIndex($i);
      if (preg_match('/\.pdf$/i', $filename)) {
        $title = preg_replace('/\.(pdf|PDF)$/', '', basename($filename));
        echo "• \"$title\"\n";
      }
    }
    $zip->close();
    echo "\nCopiez l'un de ces titres et relancez la commande.\n";
  } else {
    echo "❌ Aucun fichier ZIP trouvé\n";
  }
  exit;
}

echo "=== Analyse du titre spécifique ===\n\n";
echo "🔍 Recherche pour: \"$title_to_search\"\n";
echo str_repeat("=", 60) . "\n";

// Charger tous les nœuds arabes
$query = \Drupal::entityQuery('node')
  ->condition('type', 'reglementation')
  ->condition('langcode', 'ar')
  ->condition('status', 1)
  ->accessCheck(FALSE);

$nids = $query->execute();
$arabic_nodes = [];

if (!empty($nids)) {
  $nodes = Node::loadMultiple($nids);
  foreach ($nodes as $node) {
    $arabic_nodes[] = [
      'nid' => $node->id(),
      'title' => $node->getTitle(),
    ];
  }
}

echo "📋 " . count($arabic_nodes) . " nœuds arabes dans la base\n\n";

// Fonction pour analyser les caractères
function analyzeString($str) {
  $analysis = [
    'length' => mb_strlen($str, 'UTF-8'),
    'bytes' => strlen($str),
    'chars' => [],
    'hex' => bin2hex($str),
  ];
  
  for ($i = 0; $i < mb_strlen($str, 'UTF-8'); $i++) {
    $char = mb_substr($str, $i, 1, 'UTF-8');
    $analysis['chars'][] = [
      'char' => $char,
      'code' => mb_ord($char, 'UTF-8'),
      'hex' => bin2hex($char),
      'name' => $char === ' ' ? '[SPACE]' : ($char === '' ? '[EMPTY]' : $char),
    ];
  }
  
  return $analysis;
}

// Rechercher les correspondances
$best_match = null;
$best_similarity = 0;
$all_matches = [];

foreach ($arabic_nodes as $node) {
  similar_text($title_to_search, $node['title'], $similarity);
  
  $all_matches[] = [
    'node' => $node,
    'similarity' => $similarity,
  ];
  
  if ($similarity > $best_similarity) {
    $best_similarity = $similarity;
    $best_match = $node;
  }
}

// Trier par similarité
usort($all_matches, function($a, $b) {
  return $b['similarity'] <=> $a['similarity'];
});

// Afficher les 5 meilleures correspondances
echo "🎯 TOP 5 DES CORRESPONDANCES:\n\n";
for ($i = 0; $i < min(5, count($all_matches)); $i++) {
  $match = $all_matches[$i];
  echo sprintf("%d. Nœud #%s (%.1f%%): \"%s\"\n", 
    $i + 1,
    $match['node']['nid'],
    $match['similarity'],
    mb_substr($match['node']['title'], 0, 80, 'UTF-8') . (mb_strlen($match['node']['title'], 'UTF-8') > 80 ? '...' : '')
  );
}

if ($best_match && $best_similarity > 50) {
  echo "\n" . str_repeat("-", 80) . "\n";
  echo "🔬 ANALYSE DÉTAILLÉE DE LA MEILLEURE CORRESPONDANCE:\n\n";
  echo "Nœud #" . $best_match['nid'] . " (" . round($best_similarity, 1) . "% de similarité)\n\n";
  
  $pdf_analysis = analyzeString($title_to_search);
  $node_analysis = analyzeString($best_match['title']);
  
  echo "📄 PDF  : " . $pdf_analysis['length'] . " caractères, " . $pdf_analysis['bytes'] . " bytes\n";
  echo "🗂️  Nœud: " . $node_analysis['length'] . " caractères, " . $node_analysis['bytes'] . " bytes\n\n";
  
  echo "📄 PDF  : \"$title_to_search\"\n";
  echo "🗂️  Nœud: \"" . $best_match['title'] . "\"\n\n";
  
  if ($best_similarity < 100) {
    echo "🔍 DIFFÉRENCES CARACTÈRE PAR CARACTÈRE:\n\n";
    
    $max_length = max($pdf_analysis['length'], $node_analysis['length']);
    $differences = 0;
    
    for ($i = 0; $i < $max_length; $i++) {
      $pdf_char = $i < count($pdf_analysis['chars']) ? $pdf_analysis['chars'][$i] : null;
      $node_char = $i < count($node_analysis['chars']) ? $node_analysis['chars'][$i] : null;
      
      if (!$pdf_char && !$node_char) continue;
      
      $pdf_display = $pdf_char ? $pdf_char['name'] : '[MANQUANT]';
      $node_display = $node_char ? $node_char['name'] : '[MANQUANT]';
      
      if (!$pdf_char || !$node_char || $pdf_char['char'] !== $node_char['char']) {
        $differences++;
        echo sprintf("Pos %2d: PDF='%s' (U+%04X) | Nœud='%s' (U+%04X) ❌\n", 
          $i + 1,
          $pdf_display,
          $pdf_char ? $pdf_char['code'] : 0,
          $node_display,
          $node_char ? $node_char['code'] : 0
        );
      }
    }
    
    echo "\n📊 RÉSUMÉ: $differences différence(s) trouvée(s)\n\n";
    
    echo "🔧 SOLUTIONS RECOMMANDÉES:\n\n";
    
    if ($best_similarity > 95) {
      echo "✅ Titres très similaires - Correction simple:\n";
      echo "1. Aller sur le nœud #" . $best_match['nid'] . "\n";
      echo "2. Remplacer le titre par (copier-coller exactement):\n";
      echo "   \"$title_to_search\"\n";
      echo "3. Sauvegarder et relancer l'import\n\n";
      echo "🔗 URL d'édition: /node/" . $best_match['nid'] . "/edit\n";
    } elseif ($best_similarity > 70) {
      echo "⚠️  Différences notables - Vérification nécessaire:\n";
      echo "1. Comparer visuellement les deux titres\n";
      echo "2. Si c'est le bon nœud, corriger le titre\n";
      echo "3. Sinon, créer un nouveau nœud\n";
    } else {
      echo "❌ Différences importantes:\n";
      echo "1. Ce n'est probablement pas le bon nœud\n";
      echo "2. Créer un nouveau nœud avec le titre exact\n";
    }
  } else {
    echo "✅ CORRESPONDANCE PARFAITE!\n";
    echo "Le titre correspond exactement au nœud #" . $best_match['nid'] . "\n";
    echo "L'import devrait fonctionner normalement.\n";
  }
} else {
  echo "\n❌ AUCUNE CORRESPONDANCE PROCHE TROUVÉE\n\n";
  echo "🔧 ACTION REQUISE:\n";
  echo "1. Créer un nouveau nœud de type 'reglementation'\n";
  echo "2. Langue: Arabe (ar)\n";
  echo "3. Titre exact: \"$title_to_search\"\n";
  echo "4. Publier le nœud\n";
  echo "5. Relancer l'import\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "💡 CONSEILS:\n";
echo "• Utilisez toujours le copier-coller pour les titres arabes\n";
echo "• Attention aux espaces invisibles en début/fin\n";
echo "• Certains caractères arabes ont des codes Unicode différents\n";
echo "• Testez l'import après chaque correction\n"; 