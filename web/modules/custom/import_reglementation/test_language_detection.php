<?php

/**
 * Script de test pour la détection de langue
 * Usage: php test_language_detection.php
 */

// Charger l'autoloader de Drupal (ajustez le chemin si nécessaire)
$autoloader = require_once '../../../autoload.php';

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

try {
  // Initialiser Drupal
  $kernel = new DrupalKernel('prod', $autoloader);
  $request = Request::createFromGlobals();
  $kernel->boot();
  $kernel->preHandle($request);

  // Obtenir le service
  $pdf_processor = \Drupal::service('import_reglementation.pdf_zip_processor');

  echo "=== Test de détection de langue ===\n\n";

  // Exemples de titres à tester
  $test_titles = [
    // Titres arabes
    'قانون الأحوال الشخصية',
    'القانون الجنائي المغربي',
    'مجلة الشغل التونسية',
    'القانون رقم 123',
    'قرار وزاري رقم 456',
    
    // Titres français
    'Code de la famille',
    'Loi sur le travail',
    'Décret exécutif n°123',
    'Arrêté ministériel',
    'Code pénal français',
    
    // Titres mixtes
    'قانون رقم 123 لسنة 2023',
    'Loi n°456 الخاصة بالعمل',
    '2023 قانون العمل',
    
    // Cas limites
    '123',
    'PDF_document_final',
    'test-file-name',
    '',
  ];

  foreach ($test_titles as $title) {
    if (empty($title)) {
      $title = '[vide]';
    }
    
    echo "Titre: \"$title\"\n";
    echo "=======" . str_repeat('=', mb_strlen($title, 'UTF-8')) . "\n";
    
    // Tester la détection
    $detected = $pdf_processor->detectLanguage($title);
    
    // Analyser le contenu
    $clean_title = preg_replace('/[\s\p{P}]/u', '', $title);
    $total_chars = mb_strlen($clean_title, 'UTF-8');
    
    // Compter les caractères arabes
    $arabic_patterns = [
      '[\x{0600}-\x{06FF}]', // Arabic
      '[\x{0750}-\x{077F}]', // Arabic Supplement
      '[\x{08A0}-\x{08FF}]', // Arabic Extended-A
      '[\x{FB50}-\x{FDFF}]', // Arabic Presentation Forms-A
      '[\x{FE70}-\x{FEFF}]', // Arabic Presentation Forms-B
    ];
    
    $arabic_chars = 0;
    foreach ($arabic_patterns as $pattern) {
      $arabic_chars += preg_match_all('/' . $pattern . '/u', $clean_title);
    }
    
    // Compter les caractères latins
    $latin_chars = preg_match_all('/[a-zA-ZÀ-ÿ]/u', $clean_title);
    $numeric_chars = preg_match_all('/[0-9]/u', $clean_title);
    
    $arabic_percent = $total_chars > 0 ? ($arabic_chars / $total_chars) * 100 : 0;
    $latin_percent = $total_chars > 0 ? ($latin_chars / $total_chars) * 100 : 0;
    $numeric_percent = $total_chars > 0 ? ($numeric_chars / $total_chars) * 100 : 0;
    
    echo "Langue détectée: " . ($detected === 'ar' ? '🇸🇦 Arabe' : '🇫🇷 Français') . "\n";
    echo "Analyse:\n";
    echo "  - Caractères totaux: $total_chars\n";
    echo "  - Caractères arabes: $arabic_chars (" . round($arabic_percent, 1) . "%)\n";
    echo "  - Caractères latins: $latin_chars (" . round($latin_percent, 1) . "%)\n";
    echo "  - Chiffres: $numeric_chars (" . round($numeric_percent, 1) . "%)\n";
    
    // Tester la recherche de nœuds (simulation)
    echo "Recherche de nœuds:\n";
    echo "  - Langue prioritaire: $detected\n";
    echo "  - Langue fallback: " . ($detected === 'ar' ? 'fr' : 'ar') . "\n";
    
    echo "\n" . str_repeat('-', 50) . "\n\n";
  }

  echo "=== Test terminé ===\n";
  echo "Si la détection ne fonctionne pas comme attendu:\n";
  echo "1. Vérifiez que les titres des PDFs contiennent bien des caractères arabes\n";
  echo "2. Vérifiez que les nœuds Drupal existent dans la bonne langue\n";
  echo "3. Consultez les logs: /admin/reports/dblog\n";

} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
  echo "Assurez-vous d'exécuter ce script depuis le bon répertoire.\n";
} 