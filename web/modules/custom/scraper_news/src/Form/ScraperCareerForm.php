<?php

namespace Drupal\scraper_news\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\file\Entity\File;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\File\FileSystemInterface;
use Drupal\node\Entity\Node;

class ScraperCareerForm extends FormBase
{

  const CARRIER_URLS = [
    'fr' => 'https://www.transport.gov.ma/Gouvernance/Formation/Recrutement/Pages/Concours-de-recrutement.aspx',
    'ar' => 'https://www.transport.gov.ma/AR/gouvernance/Formation/Recrutement/Pages/Recrutements.aspx',
  ];


  public function getFormId()
  {
    return 'scraper_career_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $form['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue'),
      '#options' => [
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
      ],
      '#required' => TRUE,
      '#default_value' => 'fr',
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Scraper les carrières'),
    ];
    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $language = $form_state->getValue('language');
    $batch = [
      'title' => $this->t('Scraping des carrieres en cours...'),
      'operations' => [
        [[static::class, 'processCarrierPage'], [$language]],
      ],
      'finished' => [static::class, 'batchFinished'],
      'error_message' => $this->t('Une erreur est survenue lors du traitement.'),
      'init_message' => $this->t('Préparation du traitement...'),
    ];
    batch_set($batch);
  }

  public static function processCarrierPage($language, &$context)
  {
    $url = self::CARRIER_URLS[$language];
    $html = self::fetchHtml($url);
    $events = self::parseCarriéreHtml($html);
    echo '<pre>';
    var_dump($events);
    die;
  }

  private static function fetchHtml($url)
  {
    $ch = curl_init();
    if ($ch === false) {
      throw new \Exception('Failed to initialize cURL');
    }

    curl_setopt_array($ch, [
      CURLOPT_URL => $url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ]);

    $html = curl_exec($ch);

    if ($html === false) {
      $error = curl_error($ch);
      curl_close($ch);
      throw new \Exception("cURL error: $error");
    }

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
      throw new \Exception("HTTP request failed with status $httpCode");
    }

    return $html;
  }

  private static function parseCarriéreHtml($html)
  {
    $dom = new \DOMDocument();
    @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR);
    $xpath = new \DOMXPath($dom);

    $events = [];

    // Chercher le div principal contenant les données
    $mainDiv = $xpath->query('//div[contains(@id, "ctl00_PlaceHolderMain_ctl00__ControlWrapper_RichHtmlField")]')->item(0);

    if (!$mainDiv) {
      return $events;
    }

    // Récupérer tous les divs qui contiennent des dates (format dd/mm/yyyy)
    $dateDivs = $xpath->query('.//div[contains(., "/") and contains(., ":")]', $mainDiv);

    foreach ($dateDivs as $dateDiv) {
      $textContent = trim($dateDiv->textContent);

      // Ignorer les lignes avec des astérisques ou vides
      if (empty($textContent) || strpos($textContent, '*') !== false) {
        continue;
      }

      // Extraire la date au format dd/mm/yyyy
      if (preg_match('/(\d{2}\/\d{2}\/\d{4})/', $textContent, $dateMatches)) {
        $date = $dateMatches[1];

        // Chercher le lien PDF dans ce div
        $linkElement = $xpath->query('.//a[@href]', $dateDiv)->item(0);

        if ($linkElement) {
          $href = $linkElement->getAttribute('href');
          $linkText = trim($linkElement->textContent);

          // Nettoyer le texte du lien (enlever les extensions d'image, etc.)
          $linkText = preg_replace('/\.(pdf|PDF)$/', '', $linkText);
          $linkText = trim($linkText);

          // Construire l'URL complète si nécessaire
          $fileUrl = $href;
          if (strpos($href, 'http') !== 0) {
            // Ajouter le domaine de base si l'URL est relative
            $fileUrl = 'https://votredomaine.com' . $href; // Remplacez par votre domaine
          }

          $events[] = [
            'date' => $date,
            'title' => $linkText,
            'file_url' => $fileUrl,
            'file_name' => basename($href),
            'raw_text' => $textContent
          ];
        }
      }
    }

    return $events;
  }

  public static function CreateCareerNode($event, $language)
  {
    try {
      // Validation des données
      if (empty($event['title']) || empty($event['date'])) {
        throw new \Exception('Données manquantes pour la création du nœud');
      }

      // Conversion de la date
      $date = \DateTime::createFromFormat('d/m/Y', $event['date']);
      if (!$date) {
        throw new \Exception('Format de date invalide: ' . $event['date']);
      }

      $file = File::create([
        'filename' => $event['file_name'],
        'uri' => $event['file_url'],
        'status' => 1,
        'uid' => 1, // Utilisateur admin
      ]);
      $file->save();

      // Création du nœud
      $node = Node::create([
        'type' => 'carrieres',
        'title' => $event['title'],
        'field_date' => [
          'value' => $date->format('Y-m-d'),
          'field_document' => [
            'target_id' => $file->id(),
            'description' => $event['file_name'],
          ],
        ],
        'status' => 1,
        'langcode' => $language,
      ]);

      $node->save();

      \Drupal::logger('scraper_news')->notice('Node créé: @title', [
        '@title' => $event['title'],
      ]);
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Erreur création node: @message', [
        '@message' => $e->getMessage(),
      ]);
      throw $e;
    }
    
  }
}
